version: 2.1
commands:
  deployment:
    parameters:
      galaxy_settings:
        type: string
        default: $GALAXY_QA_SETTINGS
      galaxy_token:
        type: string
        default: $GALAXY_TOKEN
      destination:
        type: string
        default: qa
    steps:
      - attach_workspace:
          at: ~/
      - checkout
      - restore_cache:
          name: Restore project meteor cache
          key: springmath-meteor-local-{{ checksum ".meteor/versions" }}
      - restore_cache:
          name: Restore global meteor cache
          key: springmath-meteor-global-{{ checksum ".meteor/versions" }}
      - run:
          name: Install dependencies
          command: sudo apt-get install libxss1
      - run:
          name: Meteor install
          command: |
            if [ -d ~/.meteor ]; then sudo ln -s ~/.meteor/meteor /usr/local/bin/meteor; fi
            if [ ! -e $HOME/.meteor/meteor ]; then curl -sS --insecure https://install.meteor.com/?release=3.3 | /bin/sh &>/dev/null; fi
      - run:
          name: Prepare settings
          command: echo << parameters.galaxy_settings >> > ./.scripts/galaxy-settings.json
      - run:
          name: Prepare token
          command: echo << parameters.galaxy_token >> > deployment_token.json
      - run:
          name: Deploy SpringMath
          no_output_timeout: 60m
          command: source .scripts/.variables/docker_db_variables.sh && .scripts/galaxyDeploy.sh -e << parameters.destination >> -b $CIRCLE_BRANCH -s ./.scripts/galaxy-settings.json

jobs:
  build:
    docker:
      - image: cimg/node:22.16.0-browsers
        environment:
          LANG: en_US.UTF-8
          LANGUAGE: en_US.UTF-8
          LC_ALL: en_US.UTF-8
          LC_NUMERIC: en_US.UTF-8
          METEOR_BIN_TMP_DIR: /home/<USER>/build-temp/
          METEOR_BIN_TMP_FILE: meteor-bin-temp
          TERM: xterm
      - image: edspringci/edspring:spring-math-test-ci-8.0
        auth:
          username: edspringci
          password: $DOCKERHUB_PASSWORD
    working_directory: ~/project
    resource_class: medium
    steps:
      - checkout
      - restore_cache:
          name: Restore npm and deps cache
          key: springmath-npm-deps-{{ checksum "app/package.json" }}
      - restore_cache:
          name: Restore school scrubber cache
          key: springmath-school-scrubber-npm-deps-{{ checksum "app/.scripts/schoolScrubber/package.json" }}
      - restore_cache:
          name: Restore global meteor cache
          key: springmath-meteor-global-{{ checksum "app/.meteor/versions" }}
      - run:
          name: Install dependencies
          command: sudo apt-get install libxss1
      - run:
          name: SpringMath npm install
          working_directory: app
          command: if [ ! -e node_modules ]; then npm install; fi
      - run:
          name: schoolScrubber npm install
          working_directory: app/.scripts/schoolScrubber
          command: if [ ! -e node_modules ]; then npm install; fi
      - run:
          name: Meteor install
          command: |
            if [ -d ~/.meteor ]; then sudo ln -s ~/.meteor/meteor /usr/local/bin/meteor; fi
            if [ ! -e $HOME/.meteor/meteor ]; then curl -sS --insecure https://install.meteor.com/?release=2.10.0 | /bin/sh &>/dev/null; fi
      - save_cache:
          name: Save npm and deps cache
          key: springmath-npm-deps-{{ checksum "app/package.json" }}
          paths:
            - "app/node_modules"
            - "app/.meteor/local"
            - "~/.mongodb-binaries"
            - "~/.cache/Cypress"
      - save_cache:
          name: Save school scrubber cache
          key: springmath-school-scrubber-npm-deps-{{ checksum "app/.scripts/schoolScrubber/package.json" }}
          paths:
            - "app/.scripts/schoolScrubber/node_modules"
      - persist_to_workspace:
          root: ~/
          paths:
            - project
  unit_tests:
    docker:
      - image: cimg/node:22.16.0-browsers
        environment:
          LANG: en_US.UTF-8
          LANGUAGE: en_US.UTF-8
          LC_ALL: en_US.UTF-8
          LC_NUMERIC: en_US.UTF-8
          TERM: xterm
    working_directory: ~/project
    resource_class: medium
    steps:
      - attach_workspace:
          at: ~/
      - checkout
      - restore_cache:
          name: Restore npm and deps cache
          key: springmath-npm-deps-{{ checksum "app/package.json" }}
      - restore_cache:
          name: Restore school scrubber cache
          key: springmath-school-scrubber-npm-deps-{{ checksum "app/.scripts/schoolScrubber/package.json" }}
      - run:
          name: Install dependencies
          command: |
            sudo apt-get update
            sudo apt-get install libxss1
            wget http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2_amd64.deb
            sudo dpkg -i libssl1.1_1.1.1f-1ubuntu2_amd64.deb
      - run:
          name: Init memory db
          working_directory: app/imports/scripts/exportScores/.scripts
          command: if [ ! -e ~/.mongodb-binaries ]; then node initMongoDb.js; fi
      - run:
          name: Prepare output dir for test results
          command: mkdir -p ~/testResults/
      - run:
          name: Run unit tests
          working_directory: app
          command: npm run tests:unit
      - store_test_results:
          path: ~/testResults
      - store_artifacts:
          path: ~/testResults
  e2e_tests:
    docker:
      - image: cimg/node:22.16.0-browsers
        environment:
          LANG: en_US.UTF-8
          LANGUAGE: en_US.UTF-8
          LC_ALL: en_US.UTF-8
          LC_NUMERIC: en_US.UTF-8
          METEOR_BIN_TMP_DIR: /home/<USER>/build-temp/
          METEOR_BIN_TMP_FILE: meteor-bin-temp
          TERM: xterm
      - image: edspringci/edspring:spring-math-test-ci-8.0
        auth:
          username: edspringci
          password: $DOCKERHUB_PASSWORD
    working_directory: ~/project
    resource_class: medium+
    parallelism: 4
    steps:
      - attach_workspace:
          at: ~/
      - checkout
      - restore_cache:
          name: Restore npm and deps cache
          key: springmath-npm-deps-{{ checksum "app/package.json" }}
      - restore_cache:
          name: Restore school scrubber cache
          key: springmath-school-scrubber-npm-deps-{{ checksum "app/.scripts/schoolScrubber/package.json" }}
      - restore_cache:
          name: Restore project meteor cache
          key: springmath-meteor-local-{{ checksum "app/.meteor/versions" }}
      - restore_cache:
          name: Restore global meteor cache
          key: springmath-meteor-global-{{ checksum "app/.meteor/versions" }}
      - run:
          name: Install dependencies
          command: sudo apt-get install libxss1
      - run:
          name: Meteor install
          command: |
            if [ -d ~/.meteor ]; then sudo ln -s ~/.meteor/meteor /usr/local/bin/meteor; fi
            if [ ! -e $HOME/.meteor/meteor ]; then curl -sS --insecure https://install.meteor.com | /bin/sh &>/dev/null; fi
      - run:
          name: Set environmental variables
          working_directory: app
          command: .scripts/.variables/docker_db_variables.sh
      - run:
          name: Prepare output dir for test results
          command: mkdir -p ~/testResults
      - run:
          name: Run app with e2e tests
          working_directory: app
          command: |
            SPEC_LIST="$(echo $(circleci tests glob tests/cypress/integration/*.spec.js | circleci tests split --split-by=timings --show-counts --timings-type=filename) | sed 's/ /,/g')"
            echo "==============="
            echo "Running following spec files:"
            # Display converted comma separated line as rows and strip path name leaving only files and their extensions
            echo $SPEC_LIST | sed 's/,/\n/g' | awk -F'/' '{print $NF}'
            echo "==============="
            npm run tests:e2e $SPEC_LIST
      - run:
          name: Modify testResults
          when: always
          command: app/.scripts/modifyTestResults.sh
      - run:
          name: Add test metadata to testResults
          command: cp "${CIRCLE_INTERNAL_TASK_DATA}/circle-test-results/results.json" ~/testResults/results.json
      - save_cache:
          name: Save project meteor cache
          key: springmath-meteor-local-{{ checksum "app/.meteor/versions" }}
          paths:
            - "app/.meteor/local"
      - save_cache:
          name: Save global meteor cache
          key: springmath-meteor-global-{{ checksum "app/.meteor/versions" }}
          paths:
            - "~/.meteor"
      - save_cache:
          name: Save Cypress cache after e2e tests
          key: springmath-npm-deps-{{ checksum "app/package.json" }}
          paths:
            - "app/node_modules"
            - "app/.meteor/local"
            - "~/.mongodb-binaries"
            - "~/.cache/Cypress"
      - store_test_results:
          path: ~/testResults
      - store_artifacts:
          path: ~/testResults
  dev_deployment:
    docker:
      - image: cimg/node:22.16.0-browsers
        environment:
          LANG: en_US.UTF-8
          LANGUAGE: en_US.UTF-8
          LC_ALL: en_US.UTF-8
          LC_NUMERIC: en_US.UTF-8
          METEOR_BIN_TMP_DIR: /home/<USER>/build-temp/
          METEOR_BIN_TMP_FILE: meteor-bin-temp
    working_directory: ~/project/app
    resource_class: medium
    steps:
      - deployment:
          galaxy_settings: $GALAXY_DEV_SETTINGS
          destination: dev
  qa_deployment:
    docker:
      - image: cimg/node:22.16.0-browsers
        environment:
          LANG: en_US.UTF-8
          LANGUAGE: en_US.UTF-8
          LC_ALL: en_US.UTF-8
          LC_NUMERIC: en_US.UTF-8
          METEOR_BIN_TMP_DIR: /home/<USER>/build-temp/
          METEOR_BIN_TMP_FILE: meteor-bin-temp
    working_directory: ~/project/app
    resource_class: medium

    steps:
      - deployment:
          galaxy_settings: $GALAXY_QA_SETTINGS
          destination: qa
  stage_deployment:
    docker:
      - image: cimg/node:22.16.0-browsers
        environment:
          LANG: en_US.UTF-8
          LANGUAGE: en_US.UTF-8
          LC_ALL: en_US.UTF-8
          LC_NUMERIC: en_US.UTF-8
          METEOR_BIN_TMP_DIR: /home/<USER>/build-temp/
          METEOR_BIN_TMP_FILE: meteor-bin-temp
    working_directory: ~/project/app
    resource_class: medium
    steps:
      - deployment:
          galaxy_settings: $GALAXY_STAGING_SETTINGS
          destination: stage

workflows:
  version: 2

  test:
    jobs:
      - build
      - unit_tests:
          requires:
            - build
      - e2e_tests:
          requires:
            - build
            - unit_tests
      # - dev_deployment:
      #     requires:
      #       - build
      #       - unit_tests
      #     filters:
      #       branches:
      #         only: develop
      # - qa_deployment:
      #     requires:
      #       - build
      #       - unit_tests
      #       - e2e_tests
      #     filters:
      #       branches:
      #         only: release
      # - stage_deployment:
      #     requires:
      #       - build
      #       - unit_tests
      #       - e2e_tests
      #     filters:
      #       branches:
      #         only: master
