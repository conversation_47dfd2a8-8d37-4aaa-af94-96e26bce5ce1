import { Meteor } from "meteor/meteor";
import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";

import { useTracker } from "meteor/react-meteor-data";
import { areSubscriptionsLoading, isOnPrintPage } from "/imports/ui/utilities";

// Components
import GradeOverview from "./grade-overview.jsx";
import SchoolOverview from "./school-overview.jsx";
import NewsBanner from "/imports/ui/components/dashboard/news-banner";
import Loading from "/imports/ui/components/loading";
import { SchoolYearContext } from "../../../contexts/SchoolYearContext";
import { SiteContext } from "../../../contexts/SiteContext";
import { StaticDataContext } from "../../../contexts/StaticDataContext";

export const AdminOverview = ({
  loading,
  isPrinting,
  gradeId,
  bmPeriods,
  siteName,
  currentBMPeriod,
  orgid,
  siteId,
  screeningHidden,
  schoolYear
}) => {
  const [message, setMessage] = useState(null);

  const getActiveMessage = () => {
    if (!siteId && !orgid) {
      return;
    }
    Meteor.call("News:getActiveMessage", { siteId, orgid }, (err, result) => {
      if (!err) {
        setMessage(result);
      }
    });
  };

  useEffect(() => {
    getActiveMessage();
  }, [siteId, orgid]); // getActiveMessage is stable, no need to include in deps

  if (loading) {
    // TODO(fmazur) - remove message
    return (
      <div className="overviewContainer">
        <Loading />
      </div>
    );
  }

  const shouldDisplayNewsBanner = message;

  return (
    <div className={!isPrinting ? "overviewContainer" : null}>
      <div className={`news-admin-dashboard-offset ${shouldDisplayNewsBanner ? "withNewsBanner" : ""}`}>
        {shouldDisplayNewsBanner ? <NewsBanner message={message} /> : <div className="conNewsBannerPlaceholder" />}
      </div>
      {gradeId === "all" ? (
        <SchoolOverview
          bmPeriods={bmPeriods}
          siteName={siteName}
          currentBMPeriod={currentBMPeriod}
          orgid={orgid}
          siteId={siteId}
          isPrinting={isPrinting}
          screeningHidden={screeningHidden === "true"}
          schoolYear={schoolYear}
        />
      ) : (
        <GradeOverview grade={gradeId} bmPeriods={bmPeriods} siteName={siteName} currentBMPeriod={currentBMPeriod} />
      )}
    </div>
  );
};

AdminOverview.propTypes = {
  gradeId: PropTypes.string,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  siteName: PropTypes.string,
  loading: PropTypes.bool,
  grades: PropTypes.array,
  studentGroups: PropTypes.array,
  bmPeriods: PropTypes.array,
  currentBMPeriod: PropTypes.object,
  isPrinting: PropTypes.bool,
  screeningHidden: PropTypes.string,
  schoolYear: PropTypes.number
};

export const AdminOverviewWithTracker = ({ siteId, orgid, ...props }) => {
  const { schoolYear, currentBenchmarkPeriod: currentBMPeriod } = useContext(SchoolYearContext);
  const { site, studentGroupsInSite } = useContext(SiteContext);
  const { benchmarkPeriods } = useContext(StaticDataContext);

  const trackerData = useTracker(() => {
    if (!schoolYear) {
      return { loading: true };
    }

    // Subs for parent and child components
    const isPrinting = isOnPrintPage();
    Meteor.subscribe("Users", { siteId, orgid });
    const arProjection = {
      studentGroupId: 1,
      type: 1,
      status: 1,
      benchmarkPeriodId: 1,
      grade: 1,
      classwideResults: 1,
      ruleResults: 1
    };
    const assResultsSub = Meteor.subscribe("AssessmentResults:ForSite", siteId, schoolYear, arProjection);
    const gradesSub = Meteor.subscribe("GradesWithStudentGroupsInSite", siteId, schoolYear);
    const loading = areSubscriptionsLoading(gradesSub, assResultsSub);

    let siteName = "";

    if (!loading) {
      if (siteId) {
        if (site) {
          siteName = site.name;
        }
      }
    }

    return {
      loading,
      bmPeriods: benchmarkPeriods,
      siteName,
      currentBMPeriod,
      studentGroups: studentGroupsInSite,
      orgid,
      siteId,
      isPrinting,
      schoolYear
    };
  }, [schoolYear, siteId, orgid]);

  return <AdminOverview {...props} {...trackerData} />;
};

AdminOverviewWithTracker.propTypes = {
  siteId: PropTypes.string,
  orgid: PropTypes.string,
  schoolYear: PropTypes.number
};

export { AdminOverview as PureAdminOverview };
export default AdminOverviewWithTracker;
