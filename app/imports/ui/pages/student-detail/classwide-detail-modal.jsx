import React, { useEffect, useState, Component } from "react";
import { Meteor } from "meteor/meteor";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON>dalB<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import Loading from "../../components/loading";
import DetailTable from "./skillProgress";
import { openPrintWindow } from "../../utilities";

class ClasswideDetailModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isPrinting: false
    };
  }

  componentDidMount() {
    // Listen for print completion message
    window.addEventListener("message", this.handlePrintMessage);
  }

  componentWillUnmount() {
    window.removeEventListener("message", this.handlePrintMessage);
    clearTimeout(this.printTimeout);
  }

  handlePrintMessage = (event) => {
    if (event.data === "printDialogOpening") {
      // Print dialog is about to open, close the modal
      clearTimeout(this.printTimeout);
      // Small delay to ensure smooth transition before print dialog appears
      setTimeout(() => {
        this.setState({ isPrinting: false });
        this.close();
      }, 500);
    } else if (event.data === "printWindowClosed") {
      // Print dialog has been closed, reset state (fallback)
      clearTimeout(this.printTimeout);
      this.setState({ isPrinting: false });
    }
  };

  close = () => {
    this.props.onCloseModal();
  };

  handlePrint = () => {
    const { componentContext, studentGroupId, siteId, grade, schoolYear, orgid } = this.props;
    
    // Set loading state
    this.setState({ isPrinting: true });
    
    // Fallback timeout in case the message doesn't arrive
    this.printTimeout = setTimeout(() => {
      if (this.state.isPrinting) {
        this.setState({ isPrinting: false });
        this.close();
      }
    }, 10000); // 10 second fallback
    
    // Use orgid from props if available, otherwise extract from URL
    let effectiveOrgid = orgid;
    if (!effectiveOrgid) {
      const pathSegments = window.location.pathname.split('/');
      effectiveOrgid = pathSegments[1]; // orgid is always the first segment after the initial slash
    }
    
    let printURL = "";

    if (componentContext === "groupDetail") {
      // Individual Student Skill Progress
      printURL = `/${effectiveOrgid}/print/StudentSkillProgress?studentGroupId=${studentGroupId}`;
    } else if (componentContext === "gradeDetail") {
      // Skill Progress by Class
      printURL = `/${effectiveOrgid}/print/ClassSkillProgress?siteId=${siteId}&grade=${grade}&schoolYear=${schoolYear}`;
    }

    if (printURL) {
      openPrintWindow(printURL);
      // Don't close immediately - let the user close when ready
      // The spinner will reset after the print dialog appears
    }
  };

  renderModalHeaderTitle = () => {
    if (this.props.componentContext === "groupDetail") {
      return "Student Skill Progress";
    }
    if (this.props.componentContext === "gradeDetail") {
      return "Skill Progress by Class";
    }
    return "Detail Modal";
  };

  render() {
    const { showModal } = this.props;
    return (
      <Modal
        show={showModal}
        onHide={this.close}
        dialogClassName="modal-95"
        backdrop={true}
        data-testid="classwide-detail-modal"
      >
        <ModalHeader className="align-content-center justify-content-center">
          <div className="text-center">
            <h3>{this.renderModalHeaderTitle()}</h3>
          </div>
        </ModalHeader>
        <ModalBody>
          {Object.keys(this.props.rowData || {}).length ? (
            <DetailTable
              rowData={this.props.rowData}
              summaryAll={this.props.summaryAll}
              componentContext={this.props.componentContext}
            />
          ) : (
            <div className="alert alert-info text-center">No data found</div>
          )}
        </ModalBody>

        <div className="d-flex justify-content-center gap-2">
          <Button 
            variant="success" 
            onClick={this.handlePrint} 
            disabled={this.state.isPrinting}
            data-testid="print-modal-btn"
          >
            {this.state.isPrinting ? (
              <><i className="fa fa-spinner fa-spin" /> Preparing printout...</>
            ) : (
              <><i className="fa fa-print" /> Print PDF</>
            )}
          </Button>
          <Button variant="default" onClick={this.close} data-testid="cancel-modal-btn">
            Close
          </Button>
        </div>
      </Modal>
    );
  }
}

ClasswideDetailModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  onCloseModal: PropTypes.func.isRequired,
  rowData: PropTypes.object,
  summaryAll: PropTypes.object,
  componentContext: PropTypes.string,
  studentGroupId: PropTypes.string,
  siteId: PropTypes.string,
  grade: PropTypes.string,
  schoolYear: PropTypes.number,
  orgid: PropTypes.string
};

export default function ClasswideDetailModalDataWrapper(params) {
  const [loading, setLoading] = useState(true);
  const [resp, setResp] = useState({});

  useEffect(() => {
    setLoading(true);
    if (params.componentContext === "groupDetail") {
      Meteor.call(
        "getIndividualStudentDetailData",
        { studentGroupId: params.studentGroupId, shouldIncludeAllSkills: false },
        (err, r) => {
          if (!err) {
            setResp(r || {});
          }
          setLoading(false);
        }
      );
    } else if (params.componentContext === "gradeDetail") {
      Meteor.call(
        "getGradeDetailData",
        { siteId: params.siteId, grade: params.grade, schoolYear: params.schoolYear },
        (err, r) => {
          if (!err) {
            setResp(r || {});
          }
          setLoading(false);
        }
      );
    }
  }, []);

  if (loading) {
    return (
      <span className="btn btn-default">
        <Loading inline={true} message="Preparing Data..." />
      </span>
    );
  }

  return <ClasswideDetailModal {...params} rowData={resp.rowData} summaryAll={resp.summaryAll} />;
}
