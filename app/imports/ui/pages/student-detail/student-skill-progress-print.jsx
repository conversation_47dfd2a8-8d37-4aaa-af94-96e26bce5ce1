import React, { useEffect, useState, useMemo } from "react";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import url from "url";
import Loading from "../../components/loading";
import DetailTable from "./skillProgress";
import PageHeader from "../../components/page-header";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { getCurrentSchoolYear } from "/imports/api/utilities/utilities";

function StudentSkillProgressPrint() {
  const [data, setData] = useState({});
  const [dataLoading, setDataLoading] = useState(true);

  const params = url.parse(window.location.href, true).query;
  const { studentGroupId } = params;
  
  // Use useMemo to get the school year number once
  const currentSchoolYear = useMemo(() => {
    const schoolYearObj = getCurrentSchoolYear();
    return schoolYearObj?.schoolYear || new Date().getFullYear();
  }, []);

  const { studentGroup, loading: trackerLoading } = useTracker(() => {
    const sub = Meteor.subscribe("StudentGroups:ScreeningResultsData", studentGroupId, currentSchoolYear);
    return {
      studentGroup: StudentGroups.findOne(studentGroupId),
      loading: !sub.ready()
    };
  }, [studentGroupId, currentSchoolYear]);

  useEffect(() => {
    if (studentGroupId) {
      // Get skill progress data
      Meteor.call(
        "getIndividualStudentDetailData",
        { studentGroupId, shouldIncludeAllSkills: false },
        (err, result) => {
          if (!err) {
            setData(result || {});
          }
          setDataLoading(false);
        }
      );
    } else {
      setDataLoading(false);
    }
  }, [studentGroupId]);

  if (trackerLoading || dataLoading) {
    return <Loading />;
  }

  const title = `Individual Student Skill Progress${studentGroup?.name ? ` - ${studentGroup.name}` : ""}`;

  return (
    <div className="skill-progress-print-page">
      <PageHeader title={title} description="Individual student mastery progress by skill" />
      <div className="main-content">
        {Object.keys(data.rowData || {}).length ? (
          <DetailTable
            rowData={data.rowData}
            summaryAll={data.summaryAll}
            componentContext="groupDetail"
          />
        ) : (
          <div className="alert alert-info text-center">No skill progress data available</div>
        )}
      </div>
    </div>
  );
}


export default StudentSkillProgressPrint;