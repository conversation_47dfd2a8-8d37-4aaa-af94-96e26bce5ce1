import React, { useEffect, useState } from "react";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import url from "url";
import Loading from "../../components/loading";
import DetailTable from "./skillProgress";
import PageHeader from "../../components/page-header";
import { getCurrentEnrolledGrade } from "/imports/api/students/utils";
import { Sites } from "/imports/api/sites/sites";

function ClassSkillProgressPrint() {
  const [data, setData] = useState({});
  const [dataLoading, setDataLoading] = useState(true);

  const params = url.parse(window.location.href, true).query;
  const { siteId, grade: gradeParam, schoolYear } = params;

  const { site, loading: trackerLoading } = useTracker(() => {
    const sub = Meteor.subscribe("Sites", null, siteId);
    return {
      site: Sites.findOne(siteId),
      loading: !sub.ready()
    };
  }, [siteId]);

  useEffect(() => {
    if (siteId && gradeParam && schoolYear) {
      // Get skill progress data
      Meteor.call(
        "getGradeDetailData",
        { siteId, grade: gradeParam, schoolYear: parseInt(schoolYear, 10) },
        (err, result) => {
          if (!err) {
            setData(result || {});
          }
          setDataLoading(false);
        }
      );
    } else {
      setDataLoading(false);
    }
  }, [siteId, gradeParam, schoolYear]);

  if (trackerLoading || dataLoading) {
    return <Loading />;
  }

  const gradeDisplay = getCurrentEnrolledGrade(gradeParam);
  const title = `Skill Progress by Class - ${gradeDisplay}${site?.name ? ` - ${site.name}` : ""}`;

  return (
    <div className="skill-progress-print-page">
      <PageHeader title={title} description="Class-level skill mastery progress by percentage" />
      <div className="main-content">
        {Object.keys(data.rowData || {}).length ? (
          <DetailTable
            rowData={data.rowData}
            summaryAll={data.summaryAll}
            componentContext="gradeDetail"
          />
        ) : (
          <div className="alert alert-info text-center">No skill progress data available</div>
        )}
      </div>
    </div>
  );
}


export default ClassSkillProgressPrint;