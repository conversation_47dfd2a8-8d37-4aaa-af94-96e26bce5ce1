import { Meteor } from "meteor/meteor";
import React, { use<PERSON><PERSON>back, useContext, useEffect, useState } from "react";
import { get, keyBy } from "lodash";

import { ButtonGroup, Table } from "react-bootstrap";
import { Link } from "react-router-dom";

import sortByPropertyFor from "/imports/api/utilities/sortingHelpers/sortByPropertyFor";

// Utility function to format grades for display
const formatGradesForDisplay = grades => {
  if (!grades || !Array.isArray(grades) || grades.length === 0) {
    return "N/A";
  }

  // Helper function to normalize grades for sorting
  const normalizeGrade = grade => {
    if (grade === "K") return 0;
    if (grade === "HS") return 999;
    const num = parseInt(grade, 10);
    return Number.isNaN(num) ? 998 : num;
  };

  // Sort grades: K first, then numeric grades, then HS
  const sortedGrades = [...grades].sort((a, b) => normalizeGrade(a) - normalizeGrade(b));

  // Convert grade codes to display format
  const displayGrades = sortedGrades.map(grade => {
    if (grade === "K") return "K";
    if (grade === "HS") return "HS";
    // Convert "01", "02", etc. to "1", "2", etc.
    const numGrade = parseInt(grade);
    return Number.isNaN(numGrade) ? grade : numGrade.toString();
  });

  // Always list grades individually (no ranges)
  return displayGrades.join(", ");
};

import PageHeader from "../../components/page-header.jsx";
import ClientRow from "../../components/account-setup/client-row.jsx";
import { Loading } from "../../components/loading.jsx";
import { download } from "../../utilities";
import { ROLE_IDS } from "/tests/cypress/support/common/constants";
import { getCSV } from "../data-admin/upload/file-upload-utils";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";
import { UserContext } from "/imports/contexts/UserContext";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";

function ClientManagement() {
  const { schoolYear } = useContext(SchoolYearContext);
  const { currentSiteAccess } = useContext(UserContext);
  const { orgId, org: contextOrg } = useContext(OrganizationContext);
  const { isSelfEnrollee: isSML } = contextOrg || {};
  const [allOrganizations, setAllOrganizations] = useState([]);
  const [orgsToDisplay, setOrgsToDisplay] = useState([]);
  const [displayedOrgIds, setDisplayedOrgIds] = useState([]);
  const [state, setState] = useState({
    activelyEnrolledStudentsCountByOrganization: null,
    activeGradesByOrganization: null,
    sortBy: "name",
    sortOrder: 1,
    showInactiveClients: false,
    hideEmptyClients: true,
    shouldShowAllStats: false,
    isLoadingAllStats: false,
    numberOfLoadedStats: 0,
    areAllStatsLoaded: false,
    users: [],
    loading: true
  });

  const getNumberOfActiveStudents = useCallback(
    localSchoolYear => {
      Meteor.call("Students:getNumberOfActiveStudents", null, localSchoolYear, (err, resp) => {
        if (!err) {
          setState(prevState => ({
            ...prevState,
            activelyEnrolledStudentsCountByOrganization: keyBy(resp, "_id"),
            ...(!Object.keys(resp).length ? { areAllStatsLoaded: true, shouldShowAllStats: true } : {}),
            ...(prevState.activelyEnrolledStudentsCountByOrganization === null
              ? { hideEmptyClients: !!resp.length }
              : {})
          }));
        }
      });
    },
    [schoolYear]
  );

  const getActiveGradesByOrganization = useCallback(
    localSchoolYear => {
      Meteor.call("Students:getActiveGradesByOrganization", null, localSchoolYear, (err, resp) => {
        if (!err) {
          setState(prevState => ({
            ...prevState,
            activeGradesByOrganization: keyBy(resp, "_id")
          }));
        }
      });
    },
    [schoolYear]
  );

  const getAllOrganizations = useCallback(() => {
    Meteor.call("Organizations:getAllOrganizations", orgId, (err, orgs) => {
      if (!err) {
        const filteredOrgs = orgs
          .filter(org => (isSML ? org.isSelfEnrollee : !org.isSelfEnrollee))
          .map(org => ({
            ...org,
            percentScreened: -1,
            interventionConsistency: -1,
            averageWeeksPerSkill: -1
          }));
        setAllOrganizations(filteredOrgs);
        setState(prevState => ({ ...prevState, loading: false }));
      }
    });
  }, []);

  const getDataAdminsInOrganization = useCallback(() => {
    Meteor.call("Users:DataAdminsInAllOrganizations", (err, users) => {
      if (!err) {
        setState(prevState => ({ ...prevState, users }));
      }
    });
  }, []);

  useEffect(() => {
    getAllOrganizations();
    getDataAdminsInOrganization();
    getNumberOfActiveStudents(schoolYear);
    getActiveGradesByOrganization(schoolYear);
    // NOTE(fmazur) - functions in deps due to useCallback, technically not required since useCallback deps are empty
  }, [
    getNumberOfActiveStudents,
    getActiveGradesByOrganization,
    getAllOrganizations,
    getDataAdminsInOrganization,
    orgId,
    schoolYear,
    isSML
  ]);

  useEffect(() => {
    getNumberOfActiveStudents(schoolYear);
    getActiveGradesByOrganization(schoolYear);
    setState(prevState => ({
      ...prevState,
      shouldShowAllStats: false,
      isLoadingAllStats: false,
      numberOfLoadedStats: 0,
      areAllStatsLoaded: false,
      orgs: []
    }));
  }, [schoolYear, getNumberOfActiveStudents, getActiveGradesByOrganization]);

  useEffect(() => {
    let orgsToShow = [...allOrganizations];
    orgsToShow = orgsToShow.length
      ? sortByPropertyFor({ list: orgsToShow, paths: [state.sortBy], order: state.sortOrder })
      : [];

    if (state.hideEmptyClients) {
      orgsToShow = orgsToShow.filter(org => {
        const currentOrg = state.activelyEnrolledStudentsCountByOrganization?.[org._id];
        return currentOrg && currentOrg.totalStudents > 0;
      });
    }
    if (!state.showInactiveClients) {
      orgsToShow = orgsToShow.filter(org => org.isActive);
    }
    setOrgsToDisplay(orgsToShow);
    setDisplayedOrgIds(orgsToShow.length ? orgsToShow.map(org => org._id) : []);
  }, [
    allOrganizations,
    state.hideEmptyClients,
    state.showInactiveClients,
    state.activelyEnrolledStudentsCountByOrganization,
    state.sortOrder,
    state.sortBy,
    state.numberOfLoadedStats
  ]);

  const handleSorting = e => {
    const path = e.target.value;
    setState(prevState => ({ ...prevState, sortBy: path }));
  };

  const toggleShowInactiveClients = e => {
    const shouldShowIncativeClients = e?.target?.value === "withInactive";
    setState(prevState => ({ ...prevState, showInactiveClients: shouldShowIncativeClients }));
  };

  const toggleShowEmptyClients = e => {
    const shouldHideEmptyClients = e?.target?.checked;
    setState(prevState => ({ ...prevState, hideEmptyClients: shouldHideEmptyClients }));
  };

  const showAllStats = () => {
    setState(prevState => ({
      ...prevState,
      shouldShowAllStats: true,
      isLoadingAllStats: true,
      numberOfLoadedStats: 0
    }));
  };

  const onStatsLoaded = (orgid, stats) => {
    let orgs = [...allOrganizations];
    if (!state.hideEmptyClients) {
      orgs = orgs.filter(org => {
        const currentOrg = state.activelyEnrolledStudentsCountByOrganization[org._id];
        return currentOrg && currentOrg.totalStudents > 0;
      });
    }
    const numberOfOrgsWithStats = orgsToDisplay.length;
    setState(prevState => {
      const newState = {
        numberOfLoadedStats: prevState.numberOfLoadedStats + 1
      };
      if (newState.numberOfLoadedStats >= numberOfOrgsWithStats) {
        newState.isLoadingAllStats = false;
        newState.shouldShowAllStats = true;
        newState.areAllStatsLoaded = true;
      }
      return { ...prevState, ...newState };
    });
    setAllOrganizations(orgs.map(org => (org._id === orgid ? { ...org, ...stats } : org)));
  };

  const renderSortingOptions = () => {
    return (
      <span className="alignWithInlineButtons">
        Sort: &nbsp;
        <ButtonGroup>
          <label>
            <input
              type="radio"
              className="me-2 inputAlignedWithLabel"
              name="selectedGroup"
              value="name"
              checked={state.sortBy === "name"}
              onChange={handleSorting}
              data-testid="alphabetically_radio"
            />
            Alphabetically
          </label>
          &nbsp;&nbsp;&nbsp;
          <label>
            <input
              type="radio"
              className="me-2 inputAlignedWithLabel"
              name="selectedGroup"
              value="created.on"
              checked={state.sortBy === "created.on"}
              onChange={handleSorting}
              data-testid="chronologically_radio"
            />
            Chronologically
          </label>
        </ButtonGroup>
      </span>
    );
  };

  const renderClientRowVisibilityToggles = () => {
    return (
      <React.Fragment>
        <span className="pull-left">Show: &nbsp;</span>
        <ButtonGroup className="pull-left">
          <label>
            <input
              type="radio"
              className="me-2 inputAlignedWithLabel"
              name="showClients"
              value="noInactive"
              checked={!state.showInactiveClients}
              onChange={toggleShowInactiveClients}
              data-testid="hideInactiveClients"
            />
            Active Clients only
          </label>
          &nbsp;&nbsp;&nbsp;
          <label>
            <input
              type="radio"
              className="me-2 inputAlignedWithLabel"
              name="showClients"
              value="withInactive"
              checked={state.showInactiveClients}
              onChange={toggleShowInactiveClients}
              data-testid="showInactiveClients"
            />
            Include Inactive Clients
          </label>
        </ButtonGroup>
        <ButtonGroup className="pull-right">
          <label>
            <input
              type="checkbox"
              className="me-2 inputAlignedWithLabel"
              name="hideClients"
              checked={state.hideEmptyClients}
              onChange={toggleShowEmptyClients}
              data-testid="hideEmptyOrgsCheckbox"
            />
            Hide Empty Organizations
          </label>
        </ButtonGroup>
      </React.Fragment>
    );
  };

  const renderShowAllStatsButton = () => {
    const buttonText = state.areAllStatsLoaded ? "All Stats Loaded" : "Show All Stats";

    return (
      <div className="d-grid">
        <button className="btn btn-default" onClick={showAllStats} disabled={state.shouldShowAllStats}>
          {state.isLoadingAllStats ? <Loading inline message="It may take several minutes" /> : buttonText}
        </button>
      </div>
    );
  };

  const sortBy = property => () => {
    if (property !== "name" && state.numberOfLoadedStats < 3) {
      return;
    }
    const sortOrder = state.sortBy === property ? -state.sortOrder : 1;
    setState(prevState => ({ ...prevState, sortBy: property, sortOrder }));
  };

  const renderSortOrderIndicator = sortByPath => {
    if (sortByPath !== state.sortBy) {
      return null;
    }
    return state.sortOrder === 1 ? (
      <span className="fa fa-chevron-up" aria-hidden="true" />
    ) : (
      <span className="fa fa-chevron-down" aria-hidden="true" />
    );
  };

  const downloadCSV = () => {
    const headerNamesByKey = {
      name: "Name",
      state: "State",
      city: "City",
      dataAdmins: "Data Admins",
      isActive: "Account Status",
      students: "Number of Students",
      percentScreened: "% Screened",
      interventionConsistency: "Intervention Consistency (%)",
      averageWeeksPerSkill: "Average Weeks Per Skill"
    };

    const data = [];
    orgsToDisplay.forEach(org => {
      const parsedOrg = {
        name: org.name,
        state: org.details.state,
        city: org.details.city,
        dataAdmins: `${state.users
          .filter(u => u.profile.orgid === org._id)
          .map(u => `${u.profile.name.first} ${u.profile.name.last}`)
          .join("; ")}`,
        isActive: org.isActive ? "Active" : "Inactive",
        students: get(state.activelyEnrolledStudentsCountByOrganization, `[${org._id}].totalStudents`, 0),
        percentScreened: org.percentScreened,
        interventionConsistency: org.interventionConsistency,
        averageWeeksPerSkill: org.averageWeeksPerSkill
      };
      const dataElement = Object.keys(headerNamesByKey).reduce((sortedRow, headerKey) => {
        let value = parsedOrg[headerKey];

        if (value === undefined || value === null || value < 0) {
          value = "";
        } else if (typeof value === "number") {
          value = Number.isInteger(value) ? value : value.toFixed(1);
        }

        sortedRow[headerNamesByKey[headerKey]] = value;
        return sortedRow;
      }, {});
      data.push(dataElement);
    });

    const hrefData = `data:application/octet-stream,${getCSV(data)}`;
    download({
      filename: `client_list_${new Date().toISOString().slice(0, 10)}.csv`,
      hrefData
    });
  };

  const toggleActiveClientState = (id, type) => {
    if (type === "deactivate") {
      Meteor.call("deactivateOrganization", id, () => {
        getAllOrganizations();
      });
    }
    if (type === "reactivate") {
      Meteor.call("reactivateOrganization", id, () => {
        getAllOrganizations();
      });
    }
  };

  const onClientRemoved = orgid => {
    setAllOrganizations(prevState => prevState.filter(org => org._id !== orgid));
  };

  if (state.loading) {
    return <Loading />;
  }

  const userRole = currentSiteAccess?.role;
  const isSupportClientView = [ROLE_IDS.support, ROLE_IDS.universalCoach].includes(userRole);
  const isSuperAdmin = userRole === ROLE_IDS.superAdmin;
  const isUniversalDataAdmin = userRole === ROLE_IDS.universalDataAdmin;
  const isUniversalCoach = userRole === ROLE_IDS.universalCoach;
  const { activelyEnrolledStudentsCountByOrganization } = state;
  const addClientLink = isUniversalDataAdmin ? "/client-list/setup" : "/clients/setup";
  const shouldDisplayManageTestData = !!orgsToDisplay.find(org => org.isTestOrg);
  const isSortingStatsEnabled = state.numberOfLoadedStats > 2;
  let statsHeaderClass = "col-md-1 text-center";
  if (isSortingStatsEnabled) {
    statsHeaderClass += " cursor-pointer link-primary";
  }

  return (
    <div className="conFullScreen">
      <PageHeader title="Client List" />
      {isUniversalCoach ? (
        <div className="header-action-button-container">
          <button type="button" className="btn btn-primary" onClick={downloadCSV}>
            <i className="fa fa-download" /> Export List
          </button>
        </div>
      ) : null}
      <div className="container">
        <div className="row">
          <div className="col-sm-12">
            <div className="card-box">
              {isSuperAdmin || isUniversalDataAdmin ? (
                <div>
                  {!isSML && (
                    <Link to={addClientLink} className="btn btn-success" data-testid="addClientButton">
                      Add Client
                    </Link>
                  )}
                  &nbsp;
                  {renderSortingOptions()}
                  <hr />
                </div>
              ) : null}
              {renderClientRowVisibilityToggles()}
              {isSuperAdmin || activelyEnrolledStudentsCountByOrganization ? (
                <Table>
                  <thead>
                    {isSupportClientView ? (
                      <tr>
                        <th colSpan="5" />
                        <th colSpan="3">{renderShowAllStatsButton()}</th>
                      </tr>
                    ) : null}
                    <tr>
                      <th className="col-md-4 cursor-pointer link-primary" onClick={sortBy("name")}>
                        Name {renderSortOrderIndicator("name")}
                      </th>
                      <th className="col-md-2 text-center">Data Admins</th>
                      <th className="col-md-1 text-center">Account Status</th>
                      <th className="col-md-1">Currently Rostered</th>
                      <th className="col-md-1">Grades</th>
                      {isSupportClientView ? (
                        <React.Fragment>
                          <th className={statsHeaderClass} onClick={sortBy("percentScreened")}>
                            % Screened {renderSortOrderIndicator("percentScreened")}
                          </th>
                          <th className={statsHeaderClass} onClick={sortBy("interventionConsistency")}>
                            Intervention Consistency {renderSortOrderIndicator("interventionConsistency")}
                          </th>
                          <th className={statsHeaderClass} onClick={sortBy("averageWeeksPerSkill")}>
                            Average Weeks Per Skill {renderSortOrderIndicator("averageWeeksPerSkill")}
                          </th>
                        </React.Fragment>
                      ) : (
                        <React.Fragment>
                          <th className="col-md-2">Rostering</th>
                          <th className="col-md-1">Allow Classwide Without Screening</th>
                          <th className="col-md-1">Allow Multiple Grade Levels</th>
                          {!isUniversalDataAdmin && shouldDisplayManageTestData && (
                            <th className="col-md-1">Manage Test Data</th>
                          )}
                        </React.Fragment>
                      )}
                    </tr>
                  </thead>
                  {state.loading ? (
                    <tbody>
                      <tr>
                        <td>
                          <Loading />
                        </td>
                      </tr>
                    </tbody>
                  ) : (
                    <tbody>
                      {orgsToDisplay.map(org => {
                        const dataAdminUsersForThisOrg = state.users.filter(u => u.profile.orgid === org._id);
                        const studentCount = get(
                          activelyEnrolledStudentsCountByOrganization,
                          `[${org._id}].totalStudents`,
                          0
                        );
                        const orgGrades = get(state.activeGradesByOrganization, `[${org._id}].grades`, []);
                        return (
                          <ClientRow
                            org={org}
                            key={org._id}
                            dataAdminUsers={dataAdminUsersForThisOrg}
                            studentCount={studentCount}
                            grades={orgGrades}
                            isSuperAdmin={isSuperAdmin}
                            isSupportClientView={isSupportClientView}
                            isUniversalDataAdmin={isUniversalDataAdmin}
                            isHidden={!displayedOrgIds.includes(org._id)}
                            shouldDisplayManageTestData={shouldDisplayManageTestData}
                            shouldShowStats={state.shouldShowAllStats}
                            onStatsLoaded={onStatsLoaded}
                            toggleActiveClientState={toggleActiveClientState}
                            schoolYear={schoolYear}
                            onClientRemoved={onClientRemoved}
                            formatGradesForDisplay={formatGradesForDisplay}
                          />
                        );
                      })}
                    </tbody>
                  )}
                </Table>
              ) : (
                <Loading />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ClientManagement;
export { ClientManagement as PureClientManagement };
