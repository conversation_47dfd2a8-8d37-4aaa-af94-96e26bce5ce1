import React from "react";
import { shallow } from "enzyme";
import SchoolItem from "./school-item.jsx";

// Test the formatGradesForDisplay function by importing it
// Since it's not exported, we'll test it through the component
describe("SchoolItem Component", () => {
  const mockProps = {
    site: {
      _id: "site1",
      name: "Test School",
      stateInformation: {
        schoolNumber: "12345"
      },
      grades: ["K", "01", "02", "03", "04", "05"]
    },
    teachersCount: 5,
    studentsCount: 100,
    studentGroupsCount: 8,
    admins: [],
    otherSchoolNumbers: [],
    otherSchoolNames: [],
    teachersYetToOnboardCount: 2,
    loading: false,
    userRole: "dataAdmin",
    orgid: "org1",
    getSchoolItemData: jest.fn()
  };

  it("should render grades display in Demographics section", () => {
    const wrapper = shallow(<SchoolItem {...mockProps} />);
    
    // Find the Demographics section
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    expect(demographicsSection).toHaveLength(1);
    
    // Check that grades are displayed
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("Grades:");
  });

  it("should handle empty grades array", () => {
    const propsWithEmptyGrades = {
      ...mockProps,
      site: {
        ...mockProps.site,
        grades: []
      }
    };
    
    const wrapper = shallow(<SchoolItem {...propsWithEmptyGrades} />);
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("No grades available");
  });

  it("should handle missing grades field", () => {
    const propsWithoutGrades = {
      ...mockProps,
      site: {
        ...mockProps.site,
        grades: undefined
      }
    };
    
    const wrapper = shallow(<SchoolItem {...propsWithoutGrades} />);
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("No grades available");
  });

  it("should format consecutive grades as a range", () => {
    const propsWithConsecutiveGrades = {
      ...mockProps,
      site: {
        ...mockProps.site,
        grades: ["01", "02", "03", "04", "05"]
      }
    };
    
    const wrapper = shallow(<SchoolItem {...propsWithConsecutiveGrades} />);
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("1-5");
  });

  it("should handle kindergarten and high school grades", () => {
    const propsWithSpecialGrades = {
      ...mockProps,
      site: {
        ...mockProps.site,
        grades: ["K", "01", "02", "HS"]
      }
    };
    
    const wrapper = shallow(<SchoolItem {...propsWithSpecialGrades} />);
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("K");
    expect(gradesText).toContain("HS");
  });

  it("should list non-consecutive grades individually", () => {
    const propsWithNonConsecutiveGrades = {
      ...mockProps,
      site: {
        ...mockProps.site,
        grades: ["K", "02", "05", "HS"]
      }
    };
    
    const wrapper = shallow(<SchoolItem {...propsWithNonConsecutiveGrades} />);
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("K, 2, 5, HS");
  });
});
