import React from "react";
import { shallow } from "enzyme";
import SchoolItem from "./school-item.jsx";

// Test the formatGradesForDisplay function by importing it
// Since it's not exported, we'll test it through the component
describe("SchoolItem Component", () => {
  const mockProps = {
    site: {
      _id: "site1",
      name: "Test School",
      stateInformation: {
        schoolNumber: "12345"
      },
      activeGrades: ["K", "01", "02", "03", "04", "05"]
    },
    teachersCount: 5,
    studentsCount: 100,
    studentGroupsCount: 8,
    admins: [],
    otherSchoolNumbers: [],
    otherSchoolNames: [],
    teachersYetToOnboardCount: 2,
    loading: false,
    userRole: "dataAdmin",
    orgid: "org1",
    getSchoolItemData: jest.fn()
  };

  it("should render grades display in Demographics section", () => {
    const wrapper = shallow(<SchoolItem {...mockProps} />);

    // Find the Demographics section
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    expect(demographicsSection).toHaveLength(1);

    // Check that grades are displayed
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("Grades:");
  });

  it("should handle empty grades array", () => {
    const propsWithEmptyGrades = {
      ...mockProps,
      site: {
        ...mockProps.site,
        activeGrades: []
      }
    };

    const wrapper = shallow(<SchoolItem {...propsWithEmptyGrades} />);
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("N/A");
  });

  it("should handle missing grades field", () => {
    const propsWithoutGrades = {
      ...mockProps,
      site: {
        ...mockProps.site,
        activeGrades: undefined
      }
    };

    const wrapper = shallow(<SchoolItem {...propsWithoutGrades} />);
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("N/A");
  });

  it("should format grades as individual list (no ranges)", () => {
    const propsWithConsecutiveGrades = {
      ...mockProps,
      site: {
        ...mockProps.site,
        activeGrades: ["01", "02", "03", "04", "05"]
      }
    };

    const wrapper = shallow(<SchoolItem {...propsWithConsecutiveGrades} />);
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("1, 2, 3, 4, 5");
  });

  it("should handle kindergarten and high school grades", () => {
    const propsWithSpecialGrades = {
      ...mockProps,
      site: {
        ...mockProps.site,
        activeGrades: ["K", "01", "02", "HS"]
      }
    };

    const wrapper = shallow(<SchoolItem {...propsWithSpecialGrades} />);
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("K, 1, 2, HS");
  });

  it("should list non-consecutive grades individually", () => {
    const propsWithNonConsecutiveGrades = {
      ...mockProps,
      site: {
        ...mockProps.site,
        activeGrades: ["K", "02", "05", "HS"]
      }
    };

    const wrapper = shallow(<SchoolItem {...propsWithNonConsecutiveGrades} />);
    const demographicsSection = wrapper.find('[data-testid="Test School-demographics"]');
    const gradesText = demographicsSection.text();
    expect(gradesText).toContain("K, 2, 5, HS");
  });
});
