import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Link } from "react-router-dom";
import { uniq } from "lodash";
import ConfirmModal from "../../../pages/data-admin/confirm-modal";
import { Loading } from "../../loading";
import { AppDataContext } from "../../../routing/AppDataContext";
import { isExternalRostering, isSuperAdminOrUniversalDataAdminOrDataAdmin } from "../../../utilities";
import TooltipWrapper from "../../tooltip-wrapper";
import UserProfile from "../../user/user-profile";

// Utility function to format grades for display
const formatGradesForDisplay = grades => {
  if (!grades || !Array.isArray(grades) || grades.length === 0) {
    return "N/A";
  }

  // Sort grades properly (K first, then numeric, then HS)
  const sortedGrades = [...grades].sort((a, b) => {
    // Handle K/Kindergarten
    if (a === "K" || a === "00") return -1;
    if (b === "K" || b === "00") return 1;

    // Handle HS
    if (a === "HS") return 1;
    if (b === "HS") return -1;

    // Handle numeric grades
    const numA = parseInt(a);
    const numB = parseInt(b);

    if (!isNaN(numA) && !isNaN(numB)) {
      return numA - numB;
    }

    // Fallback to string comparison
    return a.localeCompare(b);
  });

  // Convert grade codes to display format
  const displayGrades = sortedGrades.map(grade => {
    switch (grade) {
      case "K":
      case "00":
        return "K";
      case "01":
        return "1";
      case "02":
        return "2";
      case "03":
        return "3";
      case "04":
        return "4";
      case "05":
        return "5";
      case "06":
        return "6";
      case "07":
        return "7";
      case "08":
        return "8";
      case "HS":
        return "HS";
      default:
        return grade;
    }
  });

  // Always list grades individually (no ranges)
  return displayGrades.join(", ");
};

class SchoolItem extends Component {
  static contextType = AppDataContext;

  constructor(props) {
    super(props);
    this.state = {
      showRemoveModal: false,
      showEmailModal: false,
      coachId: null,
      coachName: "",
      isUserAssignedToAnyGroup: false,
      isEditingSchool: false,
      schoolData: {
        schoolName: props.site?.name || "N/A",
        schoolNumber: props.site?.stateInformation?.schoolNumber || "N/A"
      },
      errors: [],
      isProfileModalOpen: false,
      selectedUserId: null
    };
  }

  openRemoveModal = ({ coachId, coachName, isUserAssignedToAnyGroup }) => {
    this.setState({ coachId, coachName, showRemoveModal: true, isUserAssignedToAnyGroup });
  };

  openEmailModal = coachId => {
    this.setState({ coachId, showEmailModal: true });
  };

  openProfileModal = userId => {
    this.setState({ isProfileModalOpen: true, selectedUserId: userId });
  };

  closeProfileModal = () => {
    this.setState({ isProfileModalOpen: false, selectedUserId: null });
  };

  removeCoach = () => {
    if (this.state.coachId) {
      Meteor.call("users:removeCoachUser", this.props.orgid, this.props.site._id, this.state.coachId, err => {
        if (err) {
          this.setState({ showModal: false });
          Alert.error(err.message, {
            timeout: 3000
          });
        } else {
          this.setState({ coachId: null, showRemoveModal: false });
          Alert.success("Coach removed", {
            timeout: 3000
          });
          this.props.getSchoolItemData();
        }
      });
    }
  };

  updateSchool = () => {
    this.setState({ isEditingSchool: false });
    Meteor.call("Sites:updateSchool", this.props.orgid, this.props.site._id, this.state.schoolData, err => {
      if (!err) {
        Alert.success("Successfully updated School");
        this.props.getSchoolItemData();
      } else {
        Alert.error(err.message);
      }
    });
  };

  onChange = e => {
    const { name, value, nextElementSibling } = e.target;
    let hasError = false;
    if (
      (name === "schoolNumber" && this.props.otherSchoolNumbers.includes(value)) ||
      (name === "schoolName" && this.props.otherSchoolNames.includes(value))
    ) {
      nextElementSibling.textContent = "Must be unique";
      nextElementSibling.className = "help-block-school-item text-nowrap text-danger animated bounceIn";
      nextElementSibling.focus();
      hasError = true;
    } else if (!value.length) {
      nextElementSibling.textContent = "Cannot be empty";
      nextElementSibling.className = "help-block-school-item text-nowrap text-danger animated bounceIn";
      nextElementSibling.focus();
      hasError = true;
    } else {
      nextElementSibling.className = "help-block-school-item d-none";
      hasError = false;
    }
    this.setState(prevState => ({
      schoolData: {
        ...prevState.schoolData,
        [name]: value
      },
      errors: uniq(hasError ? [...prevState.errors, name] : prevState.errors.filter(n => n !== name))
    }));
  };

  resendInvitationEmail = () => {
    if (this.state.coachId) {
      Meteor.call("users:sendEnrollmentEmail", this.state.coachId, this.props.orgid, err => {
        if (err) {
          this.setState({ showEmailModal: false });
          Alert.error(err.message, {
            timeout: 3000
          });
        } else {
          this.setState({ coachId: null, showEmailModal: false });
          Alert.success("Invitation email successfully resend.", {
            timeout: 3000
          });
        }
      });
    }
  };

  closeModal = () => {
    this.setState({
      showRemoveModal: false,
      showEmailModal: false,
      coachId: null
    });
  };

  getManageSchoolRoute = () => {
    const { studentGroupsCount, orgid, site } = this.props;

    if (isExternalRostering(this.context.rostering) && !studentGroupsCount) {
      return `/data-admin/rostering/${orgid}`;
    }
    const navigateToGroup = `/data-admin/manage-group/students/${orgid}/site/${site._id}`;
    const addGroupRoute = `/data-admin/manage-school/add-class-and-teacher/${orgid}/site/${site._id}`;
    return studentGroupsCount ? navigateToGroup : addGroupRoute;
  };

  render() {
    const { props } = this;
    const siteId = props.site._id;
    const addCoachLink = `/coach-account-setup/${props.orgid}/site/${siteId}`;

    const isSupportUser = this.props.userRole === "support";
    const hasAccessToEditSchool = isSuperAdminOrUniversalDataAdminOrDataAdmin(this.props.userRole);

    return (
      <div className="school-item animated zoomIn" data-testid={`schoolItem_${props.site.name}`}>
        <div className="school-name">
          <h4>
            <div className="row">
              <div className="col-lg-4 text-nowrap">
                <span className="pull-left text-start">
                  <span className="school-header">School Name:</span>
                </span>
                {hasAccessToEditSchool ? (
                  <span className={`font-18 school-item-edit ${this.state.errors.length ? "cursor-not-allowed" : ""}`}>
                    {this.state.isEditingSchool ? (
                      <button
                        className="btn p-0 text-black-50 blue-hover"
                        onClick={this.updateSchool}
                        disabled={this.state.errors.length !== 0}
                      >
                        <i className="fa fa-save"></i>
                      </button>
                    ) : (
                      <button
                        className="btn p-0 text-black-50 blue-hover"
                        onClick={() => this.setState({ isEditingSchool: true })}
                      >
                        <i className="fa fa-pencil"></i>
                      </button>
                    )}
                  </span>
                ) : null}
              </div>
              <div className="col-lg-8">
                <div className="text-start m-r-15">
                  {this.state.isEditingSchool ? (
                    <div>
                      <input
                        onChange={this.onChange}
                        value={this.state.schoolData.schoolName}
                        className="form-control input-xs"
                        name="schoolName"
                        placeholder="School Name"
                      />
                      <span className="help-block-school-item d-none" />
                    </div>
                  ) : (
                    <span className="school-header">{this.state.schoolData.schoolName}</span>
                  )}
                </div>
              </div>
            </div>
            <div className="row">
              <div className="col-lg-4 text-nowrap">
                <div className="text-start">
                  <span className="school-header">School ID:</span>
                </div>
              </div>
              <div className="col-lg-8">
                <div className="text-start m-r-15">
                  {this.state.isEditingSchool ? (
                    <div>
                      <input
                        onChange={this.onChange}
                        value={this.state.schoolData.schoolNumber}
                        className="form-control input-xs"
                        name="schoolNumber"
                        placeholder="School ID"
                      />
                      <span className="help-block-school-item d-none" />
                    </div>
                  ) : (
                    <span className="school-header">{this.state.schoolData.schoolNumber}</span>
                  )}
                </div>
              </div>
            </div>
          </h4>
        </div>
        <div className="text-center m-t-10 m-b-10">
          {this.props.userRole === "support" ? (
            <Link
              to={`/school-overview/${this.props.orgid}/all/${siteId}`}
              className="btn btn-outline-blue btn-wide btn-xs"
              data-testid={`viewAsCoach_${props.site.name}`}
            >
              View As Coach
            </Link>
          ) : (
            <Link
              to={this.getManageSchoolRoute()}
              className="btn btn-outline-blue btn-wide btn-xs"
              data-testid={`manage_${siteId}`}
            >
              Manage
            </Link>
          )}
        </div>
        <h6>Demographics</h6>
        <ul data-testid={`${props.site.name}-demographics`}>
          <li>
            <span>{props.teachersCount} Teachers onboarded</span>
          </li>
          <li>
            <span>{props.teachersYetToOnboardCount} Teachers yet to onboard</span>
          </li>
          <li>{props.loading ? <Loading inline /> : <span>{props.studentsCount} Students</span>}</li>
          <li>
            <span>{props.studentGroupsCount} Classes/Groups</span>
          </li>
          <li>
            <span>Grades: {formatGradesForDisplay(props.site.activeGrades)}</span>
          </li>
        </ul>
        <div className="conCoachesList">
          {!isSupportUser && (
            <Link
              to={addCoachLink}
              className="lnkAddCoachUser text-success pull-right"
              data-testid={`addCoach_${siteId}`}
            >
              Add Coach User <i className="fa fa-plus" />
            </Link>
          )}
          <h6 className="pull-left">Coaches</h6>
          <ul className="vertical-centered d-flex flex-column">
            {props.admins.length === 0 ? (
              <li>
                <em>No Existing Coaches</em>
              </li>
            ) : (
              props.admins
                .filter(a => a.profile.siteAccess.find(sa => sa.siteId === siteId && sa.isActive))
                .map((a, index) => {
                  const adminName = `${a.profile.name.first} ${a.profile.name.last}`;
                  const adminEmail = a.emails?.[0]?.address;
                  return (
                    <li
                      ref={index}
                      key={a._id}
                      className={`d-flex flex-row align-items-center w-100 gap-1${isSupportUser ? " m-t-5" : ""}`}
                    >
                      {adminEmail ? (
                        <TooltipWrapper
                          text={adminName}
                          tooltipText={adminEmail}
                          customClassName="school-item-user flex-grow-1 text-overflow"
                          placement="top"
                        />
                      ) : (
                        <span className="text-overflow">{adminName}</span>
                      )}
                      {!isSupportUser && (
                        <span className="d-flex flex-row gap-1">
                          <i
                            onClick={() => this.openProfileModal(a._id)}
                            className="fa fa-user blue-hover"
                            title="Profile Page"
                          />
                          <i
                            onClick={() => this.openEmailModal(a._id)}
                            className="fa fa-envelope-o red-hover"
                            title="Resend email"
                          />
                          <i
                            onClick={() =>
                              this.openRemoveModal({
                                coachId: a._id,
                                coachName: adminName,
                                isUserAssignedToAnyGroup: a.isUserAssignedToAnyGroup
                              })
                            }
                            className={`fa ${
                              a.isUserAssignedToAnyGroup ? "fa-arrow-circle-down" : "fa-trash"
                            } red-hover`}
                            data-testid="removeCoaches"
                            title={a.isUserAssignedToAnyGroup ? "Downgrade user" : "Remove user"}
                          />
                        </span>
                      )}
                    </li>
                  );
                })
            )}
          </ul>
        </div>
        <ConfirmModal
          showModal={this.state.showRemoveModal}
          confirmAction={this.removeCoach}
          onCloseModal={this.closeModal}
          bodyQuestion=""
          bodyText={
            this.state.isUserAssignedToAnyGroup
              ? `Are you sure you want to downgrade ${this.state.coachName} to a teacher role?`
              : `You are about to remove a ${this.state.coachName} coach, do you wish to continue?`
          }
        />
        <ConfirmModal
          showModal={this.state.showEmailModal}
          confirmAction={this.resendInvitationEmail}
          onCloseModal={this.closeModal}
          bodyQuestion=""
          bodyText="Do you want to resend invitation email?"
        />
        {this.state.isProfileModalOpen ? (
          <ConfirmModal
            showModal={this.state.isProfileModalOpen}
            confirmText=""
            cancelText="Close"
            customProps={{ useCustomHeader: true }}
            headerText=""
            onCloseModal={this.closeProfileModal}
            bodyQuestion=""
            bodyText={<UserProfile userId={this.state.selectedUserId} />}
            size="lg"
          />
        ) : null}
      </div>
    );
  }
}

SchoolItem.propTypes = {
  site: PropTypes.object,
  teachersCount: PropTypes.number,
  studentsCount: PropTypes.number,
  studentGroupsCount: PropTypes.number,
  admins: PropTypes.array,
  otherSchoolNumbers: PropTypes.array,
  otherSchoolNames: PropTypes.array,
  teachersYetToOnboardCount: PropTypes.number,
  loading: PropTypes.bool,
  userRole: PropTypes.string,
  orgid: PropTypes.string,
  getSchoolItemData: PropTypes.func
};

export default SchoolItem;
