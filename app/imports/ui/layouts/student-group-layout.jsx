import React, { Component } from "react";
import PropTypes from "prop-types";
import { with<PERSON>out<PERSON> } from "react-router-dom";
import { DropdownButton, Dropdown } from "react-bootstrap";
import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";

import { PRINT_OPTIONS } from "/imports/api/constants";
import DetailContentLayout from "./detail-content-layout";
import MessageNotice from "../components/message-notices/message-notice.jsx";
import { openPrintWindow } from "../utilities";
import { AppDataContext } from "../routing/AppDataContext";
import NewsBanner from "../components/dashboard/news-banner";
import Loading from "../components/loading";

const PRINT_ALL_STUDENTS_BUTTON_TEXT = "Print For All Students";

class StudentGroupLayout extends Component {
  constructor(props) {
    super(props);
    this.state = {
      disabled: false,
      printOptionsFullGraphHistoryFor: {
        classwideInterventionSkills: false
      },
      selectedPrintOption: PRINT_OPTIONS.CURRENT_CLASSWIDE_INTERVENTION,
      message: null,
      classwideInterventionSkills: false,
      individualInterventionGoalSkills: false,
      individualInterventionSkills: false,
      allDetailPrintButtonText: PRINT_ALL_STUDENTS_BUTTON_TEXT,
      eventStatus: {
        preparingZip: "Compressing printouts...",
        generatingFinished: PRINT_ALL_STUDENTS_BUTTON_TEXT
      }
    };
  }

  componentDidMount() {
    this.getActiveMessage();

    window.onmessage = event => {
      if (event.data === "preparingZip") {
        this.setState({ allDetailPrintButtonText: this.getPrintLoading(this.state.eventStatus[event.data]) });
      } else if (typeof event.data === "string" && event.data.includes("generatingProgress")) {
        const [, currentProgressString, totalProgressString] = event.data.split("=");
        const currentProgress = parseInt(currentProgressString);
        const totalProgress = parseInt(totalProgressString);
        const progressMessage = `Generated ${
          currentProgress > totalProgress ? totalProgress : currentProgress
        }/${totalProgress} printouts`;
        this.setState({ allDetailPrintButtonText: this.getPrintLoading(progressMessage) });
      } else if (event.data === "generatingFinished") {
        this.setState({ disabled: false, allDetailPrintButtonText: this.state.eventStatus[event.data] });
        window.document.title = "SpringMath";
      } else if (event.data === "printScheduled") {
        this.setState({ disabled: true });
      } else if (event.data === "printWindowClosed") {
        this.setState({ disabled: false });
      }
    };
  }

  componentDidUpdate() {
    if (
      !window.document.getElementById("print-iframe") &&
      this.state.allDetailPrintButtonText !== PRINT_ALL_STUDENTS_BUTTON_TEXT
    ) {
      this.setState({ disabled: false, allDetailPrintButtonText: this.state.eventStatus.generatingFinished });
    }
  }

  shouldShowScreeningResultsPrintButton = () => this.props.location.pathname.match(/screening\/results/);

  shouldShowClasswidePrintButton = () =>
    this.props.location.pathname.match(/classwide/) || this.context.activeNavName === "classwide";

  shouldShowStudentsPrintButton = () =>
    this.props.location.pathname.match(/students$/) || this.context.activeNavName === "students";

  shouldShowGrowthPrintButton = () => this.props.location.pathname.match(/growth$/);

  getLatestBenchmark = () => {
    return this.props.studentGroup?.history?.find(h => h.type === "benchmark");
  };

  togglePrintOptions = () => {
    this.setState(state => ({
      ...state,
      printOptionsFullGraphHistoryFor: {
        classwideInterventionSkills: !state.printOptionsFullGraphHistoryFor.classwideInterventionSkills
      }
    }));
  };

  changePrintOption = printOptionName => {
    this.setState({ selectedPrintOption: printOptionName });
  };

  getPrintButtons = () => {
    if (this.shouldShowScreeningResultsPrintButton() || this.shouldShowGrowthPrintButton()) {
      return (
        <button
          className="btn btn-success pull-right print-page"
          disabled={this.state.disabled}
          onClick={this.printPage}
        >
          {this.state.disabled ? <><i className="fa fa-spinner fa-spin" /> Preparing printout...</> : <><i className="fa fa-print" /> Print This Page</>}
        </button>
      );
    }

    if (this.shouldShowClasswidePrintButton()) {
      return (
        <div className="m-l-5 pull-right">
          <div className="m-r-5 btn-group">
            <DropdownButton variant="default" title="Print Options">
              <Dropdown.Header>
                <big>Print full history of graphs:</big>
              </Dropdown.Header>
              <Dropdown.Header>
                <label>
                  <input
                    type="checkbox"
                    checked={this.state.printOptionsFullGraphHistoryFor.classwideInterventionSkills}
                    onChange={this.togglePrintOptions}
                    className="me-2"
                  />
                  <big>Classwide Intervention Skills</big>
                </label>
              </Dropdown.Header>
            </DropdownButton>
          </div>
          <button className="btn btn-success print-page m-r-5" disabled={this.state.disabled} onClick={this.printPage}>
            {this.state.disabled ? <><i className="fa fa-spinner fa-spin" /> Preparing printout...</> : <><i className="fa fa-print" /> Print This Page</>}
          </button>
          <button
            className="btn btn-success print-page"
            disabled={this.state.disabled}
            onClick={() => {
              openPrintWindow(this.getAllStudentsClasswideInterventionGraphsUrl());
            }}
          >
            {this.state.disabled ? <><i className="fa fa-spinner fa-spin" /> Preparing printout...</> : <><i className="fa fa-print" /> Print Student Graphs</>}
          </button>
        </div>
      );
    }

    if (this.shouldShowStudentsPrintButton()) {
      return (
        <React.Fragment>
          <button
            className="btn btn-success pull-right print-page"
            disabled={this.state.disabled}
            onClick={this.printPage}
          >
            <i className="fa fa-print" />
            &nbsp;
            {this.state.allDetailPrintButtonText}
          </button>{" "}
          <div className="pull-right m-r-5 btn-group">
            <DropdownButton variant="default" title="Print Options">
              {Object.entries(PRINT_OPTIONS).map(([printOptionId, printOptionName]) => (
                <Dropdown.Header key={printOptionId}>
                  <label>
                    <input
                      type="radio"
                      checked={this.state.selectedPrintOption === printOptionName}
                      onChange={() => this.changePrintOption(printOptionName)}
                      className="me-2"
                    />
                    <big>{printOptionName}</big>
                  </label>
                </Dropdown.Header>
              ))}
            </DropdownButton>
          </div>
        </React.Fragment>
      );
    }

    return null;
  };

  getScreeningResultsPrintUrl = assessmentResultId => {
    return `/${this.props.studentGroup.orgid}/print/Screening?siteId=${this.props.siteId}&studentGroupId=${this.props.studentGroupId}&assessmentResultId=${assessmentResultId}&orgid=${this.props.studentGroup.orgid}`;
  };

  getClasswideInterventionPrintUrl = () => {
    let historyItem;
    const { siteId, studentGroupId, studentGroup, isHighSchoolGroup } = this.props;
    if (isHighSchoolGroup) {
      historyItem = studentGroup?.history[0];
    } else {
      historyItem = this.getLatestBenchmark();
    }
    return `/${studentGroup.orgid}/print/ClasswideIntervention?siteId=${siteId}&studentGroupId=${studentGroupId}&schoolYear=${studentGroup.schoolYear}&assessmentResultId=${historyItem.assessmentResultId}&orgid=${studentGroup.orgid}&selectedSkillIndex=${this.context.classwideIntervention.selectedSkillIndex}&selectedSkillAssessmentId=${this.context.classwideIntervention.selectedSkillAssessmentId}&printAllClasswideInterventionSkillsGraphs=${this.state.printOptionsFullGraphHistoryFor.classwideInterventionSkills}`;
  };

  getStudentDetailForAllStudentsPrintUrl = () => {
    const { siteId, studentGroupId, studentGroup } = this.props;
    return `/${studentGroup.orgid}/print/AllStudentDetail?siteId=${siteId}&studentGroupId=${studentGroupId}&grade=${studentGroup.grade}&studentId=&schoolYear=${studentGroup.schoolYear}&orgid=${studentGroup.orgid}&printOption=${this.state.selectedPrintOption}&printAllStudents=true`;
  };

  getGrowthPrintUrl = () => {
    return `/${this.props.studentGroup.orgid}/print/Growth?siteId=${this.props.siteId}&studentGroupId=${this.props.studentGroupId}&orgid=${this.props.studentGroup.orgid}&activeNavName=growth`;
  };

  getAllStudentsClasswideInterventionGraphsUrl = () => {
    const { siteId, studentGroupId, studentGroup } = this.props;
    const currentlySelectedAssessmentId =
      this.context.classwideIntervention.selectedSkillAssessmentId || studentGroup.currentClasswideSkill.assessmentId;
    return `/${studentGroup.orgid}/print/AllStudentsClasswideInterventionGraphs?siteId=${siteId}&studentGroupId=${studentGroupId}&grade=${studentGroup.grade}&schoolYear=${studentGroup.schoolYear}&orgid=${studentGroup.orgid}&selectedSkillAssessmentId=${currentlySelectedAssessmentId}&printOnlyClasswideInterventionSkillGraphs=true`;
  };

  printPage = () => {
    const { pathname } = this.props.location;
    let printURL;
    if (this.shouldShowStudentsPrintButton()) {
      // eslint-disable-next-line no-unused-expressions
      window.document.getElementById("print-iframe")?.remove();
      window.document.title = "SpringMath";
      printURL = this.getStudentDetailForAllStudentsPrintUrl();
      this.setState({ allDetailPrintButtonText: this.getPrintLoading("Fetching data...") });
    } else if (this.shouldShowScreeningResultsPrintButton()) {
      printURL = this.getScreeningResultsPrintUrl(pathname.substring(pathname.lastIndexOf("/") + 1, pathname.length));
    } else if (this.shouldShowClasswidePrintButton()) {
      printURL = this.getClasswideInterventionPrintUrl();
    } else if (this.shouldShowGrowthPrintButton()) {
      printURL = this.getGrowthPrintUrl();
    }
    openPrintWindow(printURL);
  };

  getActiveMessage = () => {
    const { siteId, orgid } = this.props.studentGroup;
    if (!siteId && !orgid) {
      return;
    }
    Meteor.call("News:getActiveMessage", { siteId, orgid }, (err, message) => {
      if (err) {
        Alert.error("There was a problem getting active message.", err.reason || err.message);
      } else {
        this.setState({ message });
      }
    });
  };

  getPrintLoading = message => {
    return (
      <React.Fragment>
        <Loading inline={true} /> <span>{message}</span>
      </React.Fragment>
    );
  };

  render() {
    return (
      <div className="workspace-container">
        {this.state.message ? <NewsBanner message={this.state.message} /> : null}
        <div className="student-group-layout-header">
          {this.getPrintButtons()}
          <h2 id="group-name" className="w9" data-testid={`student-group_${this.props.groupName}`}>
            {this.props.groupName}
          </h2>
          {this.props.isHighSchoolGroup ? null : (
            <MessageNotice
              noticeLocation="side-nav-layout"
              expandedStateCB={this.props.expandedStateCB}
              expandedNoticeState={this.props.expandedNoticeState}
            />
          )}
        </div>
        <DetailContentLayout content={this.props.content} />
      </div>
    );
  }
}

StudentGroupLayout.propTypes = {
  content: PropTypes.object,
  groupName: PropTypes.string,
  expandedStateCB: PropTypes.func,
  messageNotice: PropTypes.object,
  expandedNoticeState: PropTypes.bool,
  isHighSchoolGroup: PropTypes.bool,
  studentGroupId: PropTypes.string,
  siteId: PropTypes.string,
  orgid: PropTypes.string,
  studentGroup: PropTypes.object,
  location: PropTypes.object
};

export default withRouter(StudentGroupLayout);
StudentGroupLayout.contextType = AppDataContext;
