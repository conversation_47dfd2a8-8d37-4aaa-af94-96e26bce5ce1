/* eslint-disable no-unused-vars */
import MockDate from "mockdate";
import { ObjectId } from "mongodb";
import { Meteor } from "meteor/meteor";
import { fetch } from "meteor/fetch";
import { Organizations } from "/imports/api/organizations/organizations";
import { News } from "/imports/api/news/news";
import { GroupedAssessments } from "/imports/api/groupedAssessments/groupedAssessments";
import { Assessments } from "/imports/api/assessments/assessments";
import { Interventions } from "/imports/api/interventions/interventions";
import { AssessmentGrowth } from "/imports/api/assessmentGrowth/assessmentGrowth";
import { Users } from "/imports/api/users/users";
import { Roles } from "/imports/api/roles/roles";
import { keyBy } from "lodash";
import { updateAvailableVideoList } from "../../api/assessments/server/methods";
import { RosterImports } from "../../api/rosterImports/rosterImports";
import { ROSTER_IMPORTS_LIMIT } from "../../api/constants";
import { trimRosterImportsAndRosterImportItemsCollectionsForOrg } from "../../api/rosterImports/methods";
import { Settings } from "../../api/settings/settings";
import { Students } from "../../api/students/students";
import { ninjalog } from "/imports/api/utilities/utilities";
import { getIndividualResultsMessage } from "../../api/assessmentResults/utilities";
import { AssessmentResults } from "../../api/assessmentResults/assessmentResults";
import { StudentGroupEnrollments } from "../../api/studentGroupEnrollments/studentGroupEnrollments";
import { getTimestampInfo } from "../../api/helpers/getTimestampInfo";

/**
 * This file contains methods that can be run during application startup
 * Methods can be called here or in imports/startup/server/index.js
 * Implementing autorun methods makes it possible to avoid the need to manually run e.g. mongo scripts
 */

async function addDownloaderRole() {
  if (await Roles.findOneAsync({ name: "downloader" })) {
    return;
  }

  console.log("Creating the missing Downloader role...");
  await Roles.insertAsync({
    _id: "arbitraryIddownloader",
    internalWeight: 0,
    label: "downloader",
    name: "downloader",
    sortOrder: 25,
    lastModified: {
      by: "SYSTEM",
      on: 1480700202213,
      date: new Date("2016-12-02T17:36:42.213Z")
    }
  });
}

export async function syncInterventionProtocols() {
  if (!Meteor.settings.public.ENVIRONMENT || Meteor.settings.public.ENVIRONMENT === "LOCAL") {
    return;
  }
  if (!Meteor.settings.PDF_GENERATOR_ENDPOINT) {
    return;
  }

  const endpoint = `${Meteor.settings.PDF_GENERATOR_ENDPOINT}/getProtocols`;
  let response;
  try {
    response = await fetch(endpoint, {
      method: "GET",
      signal: AbortSignal.timeout(120000)
    });
  } catch (e) {
    console.log(`SyncInterventionProtocols request error: `, e.message);
    return;
  }

  if (response.status !== 200) {
    return;
  }

  let interventionProtocolsByMeasure;
  try {
    interventionProtocolsByMeasure = await response.json();
  } catch (e) {
    console.log("Failed to parse JSON response from PDF generator:", e.message);
    return;
  }

  console.log("Syncing monitorAssessmentMeasures: ", Object.keys(interventionProtocolsByMeasure)?.length || 0);
  Object.entries(interventionProtocolsByMeasure).forEach(async ([monitorAssessmentMeasure, monitorProtocols]) => {
    await Assessments.updateAsync({ monitorAssessmentMeasure }, { $set: { monitorProtocols } });
  });
}

export async function migrateOrganizationField() {
  const orgsToUpdate = await Organizations.find(
    { allowRosterImports: { $exists: true } },
    { fields: { _id: 1, allowRosterImports: 1 } }
  ).fetchAsync();
  if (orgsToUpdate && orgsToUpdate.length) {
    console.log(
      "Migrating allowRosterImports to rostering for organizations",
      orgsToUpdate.map(org => org._id)
    );
    orgsToUpdate.forEach(async ({ _id, allowRosterImports }) => {
      const rostering = allowRosterImports ? "rosterImport" : "rosterUpload";
      const lastModified = await getTimestampInfo("autorun_script", null, "migrateOrganizationField");
      await Organizations.updateAsync(
        { _id },
        { $unset: { allowRosterImports: "" }, $set: { rostering, lastModified } }
      );
    });
  }
}

export async function setRosteringMessage() {
  const existingRosteringMessage = await News.findOneAsync({ type: "rostering" });
  if (existingRosteringMessage) {
    return;
  }
  // eslint-disable-next-line no-console
  console.log("Updating News collection");
  const customMessage = {
    learnMoreUrl:
      "https://springmath-support.zendesk.com/hc/en-us/signin?return_to=https://springmath-support.zendesk.com/hc/en-us/articles/4402435212183",
    learnMoreActive: false,
    messageContent: "Learn about Ed-Fi and OneRoster options for SpringMath",
    messageColor: "#FD595C",
    messageTextColor: "#ffffff",
    type: "rostering"
  };
  await News.insertAsync(customMessage);
}

export async function setRosteringThreshold() {
  const DEFAULT_ROSTERING_THRESHOLD = 0.25;
  const existingRosteringThreshold = await Settings.findOneAsync({}, { fields: { "defaults.rosteringThreshold": 1 } })
    ?.defaults?.rosteringThreshold;
  if (existingRosteringThreshold === DEFAULT_ROSTERING_THRESHOLD) {
    return;
  }
  // eslint-disable-next-line no-console
  console.log(`Setting Rostering Threshold: ${DEFAULT_ROSTERING_THRESHOLD}`);

  await Settings.updateAsync({}, { $set: { "defaults.rosteringThreshold": DEFAULT_ROSTERING_THRESHOLD } });
}

export async function updateGroupedAssessmentsWithDifficultyOrder() {
  const isAlreadyUpdated = !!(await GroupedAssessments.findOneAsync({
    skillName: "Sums",
    protocolToUse: { $exists: false }
  }));
  if (isAlreadyUpdated) {
    return;
  }
  // eslint-disable-next-line no-console
  console.log("Updating GroupedAssessments collection");

  const assessmentMeasuresBySkillGroupName = {
    "2-Digit Multiplication": { assessmentMeasures: ["76", "77", "29"] },
    "Add & Subtract Multi-Digit Numbers": { assessmentMeasures: ["67", "56"] },
    "Add and Subtract Mixed Numbers with Like Denominators and Regrouping": { assessmentMeasures: ["121"] },
    Addition: { assessmentMeasures: ["39", "52", "26", "81"] },
    "Convert Percentages to Decimals": { assessmentMeasures: ["96"] },
    "Converting between Mixed Numbers and Improper Fractions": { assessmentMeasures: ["64", "75"] },
    "Converting Fractions to Decimals and Decimals to Fractions": { assessmentMeasures: ["91", "92", "93"] },
    "Count & Circle or Write Number": { assessmentMeasures: ["150", "36", "88", "89", "90"] },
    "Creating Equivalent Addition & Subtraction Expressions and Quantity Comparison": {
      assessmentMeasures: ["106", "119", "122"]
    },
    "Creating Equivalent Fractions": { assessmentMeasures: ["130", "63", "57"] },
    "Decimal Operations": { assessmentMeasures: ["58", "48", "99"] },
    "Distribute/Collect": { assessmentMeasures: ["144", "145"] },
    "Divide with Exponents": { assessmentMeasures: ["142", "143"] },
    "Division Facts": { assessmentMeasures: ["70", "42", "72", "148"] },
    Division: { assessmentMeasures: ["73", "46", "74"] },
    "Fact Families Add/Sub": { assessmentMeasures: ["38", "51", "25"] },
    "Fact Families Multiplication & Division 0-12": { assessmentMeasures: ["28"] },
    "Find Percent of a Whole Number": { assessmentMeasures: ["167"] },
    "Fraction Equivalence": { assessmentMeasures: ["166", "120"] },
    "Fraction Operations": { assessmentMeasures: ["31", "60"] },
    Fractions: { assessmentMeasures: ["129", "98", "97"] },
    "Identify & Draw Circles": { assessmentMeasures: ["139", "20"] },
    Integers: { assessmentMeasures: ["154", "104", "127"] },
    "Inverse Operations to Solve for Unknown": { assessmentMeasures: ["105", "151", "108"] },
    "Linear Equations": { assessmentMeasures: ["131", "133", "134"] },
    "Missing Number": { assessmentMeasures: ["100", "101"] },
    "Mixed Operations": { assessmentMeasures: ["30"] },
    "More/Less Quantity Discrim w Dots": { assessmentMeasures: ["109", "110"] },
    "Multiplication Facts": { assessmentMeasures: ["66", "41", "68", "69"] },
    Multiplication: { assessmentMeasures: ["54", "55", "27"] },
    "Multiply & Divide Fractions": { assessmentMeasures: ["86", "87", "59", "47"] },
    "Number Naming": { assessmentMeasures: ["123", "124"] },
    "Order of Operations": { assessmentMeasures: ["102", "169"] },
    "Quantity Comparison w Negative Numbers": { assessmentMeasures: ["168"] },
    "Quantity Comparison": { assessmentMeasures: ["113", "112", "111"] },
    "Simplify Expressions": { assessmentMeasures: ["137", "146", "152"] },
    "Slope & Graphing": { assessmentMeasures: ["155", "158", "153", "156", "157"] },
    "Solving Equations with Percentages": { assessmentMeasures: ["125", "128"] },
    "Substitute Whole Numbers to Solve Equations": { assessmentMeasures: ["61"] },
    "Subtraction Facts": { assessmentMeasures: ["22", "50", "32", "33", "24"] },
    Subtraction: { assessmentMeasures: ["40", "53", "62", "82"] },
    Sums: { assessmentMeasures: ["21", "37", "23"] },
    "Translate verbal expressions into math equations": { assessmentMeasures: ["136"] },
    "Two-Step Equations": { assessmentMeasures: ["118", "135"] },
    "Sums and subtraction for Kinder": { assessmentMeasures: ["83", "84"] }
  };

  await GroupedAssessments.removeAsync({});
  Object.entries(assessmentMeasuresBySkillGroupName).forEach(async ([skillName, protocol]) => {
    const { assessmentMeasures } = protocol;
    // Behaves differently in older mongodb versions
    const newId = new ObjectId().valueOf().toString();
    await GroupedAssessments.insertAsync({
      _id: newId,
      skillName,
      assessmentMeasures
    });
  });
}

export async function cleanUpCollections() {
  const doCollectionsNeedCleaning =
    !!(await Assessments.findOneAsync({ externalId: { $exists: true } })) ||
    !!(await Interventions.findOneAsync({ siteIds: { $exists: true } }));
  if (!doCollectionsNeedCleaning) {
    return;
  }
  console.log("Cleaning fields for Assessments and Intervention collections");
  await Assessments.updateAsync(
    {},
    {
      $unset: {
        benchmarkSettings: 1,
        canCalculateWarningIndicator: 1,
        children: 1,
        created: 1,
        description: 1,
        differentVersionsByGradeLevel: 1,
        externalId: 1,
        hasSampleItemsToPass: 1,
        isBenchmark: 1,
        isBenchmarkPunchout: 1,
        isComposite: 1,
        isExternallyLoaded: 1,
        isLabAssessment: 1,
        isProgressMonitoring: 1,
        isProgressMonitoringPunchout: 1,
        lastModified: 1,
        orgid: 1,
        priorYearId: 1,
        progressMonitoringSettings: 1,
        researchBase: 1,
        schoolYear: 1,
        siteIds: 1,
        subjectArea: 1,
        "strands.$[].id": 1,
        "strands.$[].isOverall": 1,
        "strands.$[].scores.$[].allowNAScores": 1,
        "strands.$[].scores.$[].denotesCorrect": 1,
        "strands.$[].scores.$[].denotesError": 1,
        "strands.$[].scores.$[].highestScore": 1,
        "strands.$[].scores.$[].id": 1,
        "strands.$[].scores.$[].isGraphable": 1,
        "strands.$[].scores.$[].lowerValuesAreBetter": 1,
        "strands.$[].scores.$[].lowestScore": 1,
        "strands.$[].scores.$[].showOnStudentProfile": 1,
        "strands.$[].scores.$[].targets.$[].id": 1,
        "strands.$[].scores.$[].targets.$[].periods.$[].hasScores": 1,
        "strands.$[].scores.$[].targets.$[].periods.$[].id": 1,
        "strands.$[].scores.$[].warningIndicatorEligible": 1,
        vendorId: 1
      }
    },
    { multi: true }
  );
  await Interventions.updateAsync(
    {},
    {
      $unset: {
        areaOfConcern: 1,
        created: 1,
        duration: 1,
        groupSize: 1,
        hidden: 1,
        lastModified: 1,
        location: 1,
        minutesPerWeek: 1,
        nameLink: 1,
        orgid: 1,
        priorYearId: 1,
        researchBase: 1,
        schedule: 1,
        schoolYear: 1,
        siteIds: 1,
        skills: 1,
        specialMaterials: 1,
        subjectArea: 1
      }
    },
    { multi: true }
  );
}

export async function updateAssessmentGrowthCollection() {
  const assessmentsByMeasure = {};
  (await Assessments.find({}, { monitorAssessmentMeasure: 1 }).fetchAsync()).forEach(a => {
    assessmentsByMeasure[a.monitorAssessmentMeasure] = a._id;
    // console.log({
    //   _id: a._id
    //   name: a.name,
    //   monitorAssessmentMeasure: a.monitorAssessmentMeasure
    // })
  });
  // NOTE(fmazur) - Modify condition whether collection needs to be updated
  const assessmentGrowthDoc = await AssessmentGrowth.findOneAsync({ grade: "06" });
  if (assessmentGrowthDoc?.fallToWinter[2]?.fall === assessmentsByMeasure["99"]) {
    return;
  }
  console.log("Updating AssessmentGrowth collection");
  const growthGradeMap = {
    gradeK: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["36"], // series1
          winter: assessmentsByMeasure["90"],
          classwide: assessmentsByMeasure["36"]
        },
        {
          fall: assessmentsByMeasure["139"], // series2
          winter: assessmentsByMeasure["20"],
          classwide: assessmentsByMeasure["139"]
        },
        {
          fall: assessmentsByMeasure["109"], // series 3
          winter: assessmentsByMeasure["110"],
          classwide: assessmentsByMeasure["109"]
        },
        {
          fall: assessmentsByMeasure["100"], // series 4
          winter: assessmentsByMeasure["101"],
          classwide: assessmentsByMeasure["100"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["90"],
          classwide: assessmentsByMeasure["90"]
        },
        {
          winter: assessmentsByMeasure["20"],
          spring: assessmentsByMeasure["132"],
          classwide: assessmentsByMeasure["20"]
        },
        {
          winter: assessmentsByMeasure["110"],
          spring: assessmentsByMeasure["132"],
          classwide: assessmentsByMeasure["110"]
        },
        {
          winter: assessmentsByMeasure["101"],
          spring: assessmentsByMeasure["101"],
          classwide: assessmentsByMeasure["101"]
        }
      ]
    },
    grade01: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["21"],
          winter: assessmentsByMeasure["37"],
          classwide: assessmentsByMeasure["21"]
        },
        {
          fall: assessmentsByMeasure["22"],
          winter: assessmentsByMeasure["50"],
          classwide: assessmentsByMeasure["22"]
        },
        {
          fall: assessmentsByMeasure["111"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["37"],
          spring: assessmentsByMeasure["23"],
          classwide: assessmentsByMeasure["37"]
        },
        {
          winter: assessmentsByMeasure["50"],
          spring: assessmentsByMeasure["24"],
          classwide: assessmentsByMeasure["50"]
        },
        {
          winter: assessmentsByMeasure["38"],
          spring: assessmentsByMeasure["51"],
          classwide: assessmentsByMeasure["38"]
        }
      ]
    },
    grade02: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["23"],
          winter: assessmentsByMeasure["39"],
          classwide: assessmentsByMeasure["23"]
        },
        {
          fall: assessmentsByMeasure["24"],
          winter: assessmentsByMeasure["40"],
          classwide: assessmentsByMeasure["24"]
        },
        {
          fall: assessmentsByMeasure["25"],
          winter: assessmentsByMeasure["122"],
          classwide: assessmentsByMeasure["25"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["39"],
          spring: assessmentsByMeasure["52"],
          classwide: assessmentsByMeasure["39"]
        },
        {
          winter: assessmentsByMeasure["40"],
          spring: assessmentsByMeasure["53"],
          classwide: assessmentsByMeasure["40"]
        },
        {
          winter: assessmentsByMeasure["122"],
          classwide: assessmentsByMeasure["122"]
        },
        {
          winter: assessmentsByMeasure["106"],
          spring: assessmentsByMeasure["119"],
          classwide: assessmentsByMeasure["106"]
        }
      ]
    },
    grade03: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["25"],
          classwide: assessmentsByMeasure["25"]
        },
        {
          fall: assessmentsByMeasure["26"],
          classwide: assessmentsByMeasure["26"]
        },
        {
          fall: assessmentsByMeasure["62"],
          classwide: assessmentsByMeasure["62"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["41"],
          spring: assessmentsByMeasure["27"],
          classwide: assessmentsByMeasure["41"]
        },
        {
          winter: assessmentsByMeasure["42"],
          spring: assessmentsByMeasure["73"],
          classwide: assessmentsByMeasure["42"]
        },
        {
          winter: assessmentsByMeasure["43"],
          classwide: assessmentsByMeasure["43"]
        }
      ]
    },
    grade04: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["28"],
          classwide: assessmentsByMeasure["28"]
        },
        {
          fall: assessmentsByMeasure["27"],
          winter: assessmentsByMeasure["29"],
          classwide: assessmentsByMeasure["27"]
        },
        {
          fall: assessmentsByMeasure["97"],
          winter: assessmentsByMeasure["130"],
          classwide: assessmentsByMeasure["97"]
        },
        {
          fall: assessmentsByMeasure["107"],
          classwide: assessmentsByMeasure["107"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["29"],
          spring: assessmentsByMeasure["117"],
          classwide: assessmentsByMeasure["76"]
        },
        {
          winter: assessmentsByMeasure["121"],
          spring: assessmentsByMeasure["56"],
          classwide: assessmentsByMeasure["121"]
        },
        {
          winter: assessmentsByMeasure["130"],
          spring: assessmentsByMeasure["94"],
          classwide: assessmentsByMeasure["130"]
        }
      ]
    },
    grade05: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["28"],
          winter: assessmentsByMeasure["64"],
          classwide: assessmentsByMeasure["28"]
        },
        {
          fall: assessmentsByMeasure["56"],
          winter: assessmentsByMeasure["94"],
          classwide: assessmentsByMeasure["56"]
        },
        {
          fall: assessmentsByMeasure["29"],
          classwide: assessmentsByMeasure["29"]
        },
        {
          fall: assessmentsByMeasure["63"],
          winter: assessmentsByMeasure["31"],
          classwide: assessmentsByMeasure["63"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["64"],
          spring: assessmentsByMeasure["57"],
          classwide: assessmentsByMeasure["64"]
        },
        {
          winter: assessmentsByMeasure["31"],
          spring: assessmentsByMeasure["59"],
          classwide: assessmentsByMeasure["31"]
        },
        {
          winter: assessmentsByMeasure["94"],
          spring: assessmentsByMeasure["95"]
        }
      ]
    },
    grade06: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["31"],
          winter: assessmentsByMeasure["60"],
          classwide: assessmentsByMeasure["31"]
        },
        {
          fall: assessmentsByMeasure["102"],
          winter: assessmentsByMeasure["144"],
          classwide: assessmentsByMeasure["102"]
        },
        {
          fall: assessmentsByMeasure["99"],
          winter: assessmentsByMeasure["145"],
          classwide: assessmentsByMeasure["99"]
        },
        {
          fall: assessmentsByMeasure["47"],
          winter: assessmentsByMeasure["167"],
          classwide: assessmentsByMeasure["47"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["145"],
          spring: assessmentsByMeasure["61"],
          classwide: assessmentsByMeasure["145"]
        },
        {
          winter: assessmentsByMeasure["144"],
          classwide: assessmentsByMeasure["144"]
        },
        {
          winter: assessmentsByMeasure["167"],
          classwide: assessmentsByMeasure["167"]
        },
        {
          winter: assessmentsByMeasure["60"],
          spring: assessmentsByMeasure["99"],
          classwide: assessmentsByMeasure["60"]
        }
      ]
    },
    grade07: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["120"],
          winter: assessmentsByMeasure["166"],
          classwide: assessmentsByMeasure["120"]
        },
        {
          fall: assessmentsByMeasure["128"],
          winter: assessmentsByMeasure["151"],
          classwide: assessmentsByMeasure["128"]
        },
        {
          fall: assessmentsByMeasure["127"],
          winter: assessmentsByMeasure["102"],
          classwide: assessmentsByMeasure["127"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["102"],
          spring: assessmentsByMeasure["136"],
          classwide: assessmentsByMeasure["102"]
        },
        {
          winter: assessmentsByMeasure["151"],
          spring: assessmentsByMeasure["118"],
          classwide: assessmentsByMeasure["105"]
        },
        {
          winter: assessmentsByMeasure["166"],
          spring: assessmentsByMeasure["135"],
          classwide: assessmentsByMeasure["166"]
        }
      ]
    },
    grade08: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["146"],
          winter: assessmentsByMeasure["143"],
          classwide: assessmentsByMeasure["146"]
        },
        {
          fall: assessmentsByMeasure["137"],
          winter: assessmentsByMeasure["169"],
          classwide: assessmentsByMeasure["137"]
        },
        {
          fall: assessmentsByMeasure["153"],
          winter: assessmentsByMeasure["157"],
          classwide: assessmentsByMeasure["153"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["143"],
          classwide: assessmentsByMeasure["143"]
        },
        {
          winter: assessmentsByMeasure["169"],
          spring: assessmentsByMeasure["131"],
          classwide: assessmentsByMeasure["169"]
        },
        {
          winter: assessmentsByMeasure["157"],
          classwide: assessmentsByMeasure["157"]
        }
      ]
    }
  };

  const programEvaluationExceptions = {
    grade05: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["28"]
        },
        {
          fall: assessmentsByMeasure["56"]
        },
        {
          fall: assessmentsByMeasure["29"]
        },
        {
          fall: assessmentsByMeasure["63"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["64"],
          spring: assessmentsByMeasure["59"],
          classwide: assessmentsByMeasure["64"]
        },
        {
          winter: assessmentsByMeasure["31"],
          spring: assessmentsByMeasure["58"],
          classwide: assessmentsByMeasure["31"]
        },
        {
          winter: assessmentsByMeasure["94"],
          spring: assessmentsByMeasure["95"]
        },
        {
          spring: assessmentsByMeasure["57"],
          classwide: assessmentsByMeasure["63"]
        }
      ]
    },
    grade06: {
      fallToWinter: [
        {
          fall: assessmentsByMeasure["31"]
        },
        {
          fall: assessmentsByMeasure["102"]
        },
        {
          fall: assessmentsByMeasure["99"]
        },
        {
          fall: assessmentsByMeasure["47"]
        }
      ],
      winterToSpring: [
        {
          winter: assessmentsByMeasure["60"],
          spring: assessmentsByMeasure["99"],
          classwide: assessmentsByMeasure["60"]
        },
        {
          winter: assessmentsByMeasure["144"],
          classwide: assessmentsByMeasure["144"]
        },
        {
          winter: assessmentsByMeasure["145"],
          spring: assessmentsByMeasure["61"],
          classwide: assessmentsByMeasure["145"]
        },
        {
          winter: assessmentsByMeasure["167"],
          classwide: assessmentsByMeasure["167"]
        }
      ]
    }
  };

  ["K", "01", "02", "03", "04", "05", "06", "07", "08"].forEach(async grade => {
    const gradeKey = `grade${grade}`;
    await AssessmentGrowth.updateAsync(
      { grade },
      {
        $set: {
          fallToWinter: growthGradeMap[gradeKey].fallToWinter,
          winterToSpring: growthGradeMap[gradeKey].winterToSpring,
          programEvaluation: programEvaluationExceptions[gradeKey]
        }
      }
    );
  });
}

export function setCustomTime(periodName, customDate, unfreezeTime = false) {
  const dateByPeriodName = {
    Fall: "2019-08-02",
    Winter: "2020-01-02",
    Spring: "2020-04-02"
  };
  const dateToUse = customDate || dateByPeriodName[periodName];
  MockDate.set(dateToUse);
  if (unfreezeTime) {
    setTimeout(() => {
      setInterval(setTime, 1000);
      setTime(dateToUse);
    }, 30000);
  }
}

function setTime(date) {
  MockDate.reset();
  const today = new Date();
  const time = new Date(`${date}T${today.toISOString().split("T")[1]}`);
  // eslint-disable-next-line no-console
  console.log("New time: ", time);
  MockDate.set(time);
}

async function forceCleanUpExpiredTokens() {
  ninjalog.log({
    msg: "========================================",
    context: "forceCleanUpExpiredTokens"
  });
  ninjalog.log({
    msg: "Starting token cleanup process...",
    context: "forceCleanUpExpiredTokens"
  });

  const today = new Date();
  const oldestValidDate = new Date(new Date().setDate(today.getDate() - 90));
  const oldestValidDateTimestamp = oldestValidDate.getTime();

  ninjalog.log({
    msg: "Token cleanup date calculation",
    val: {
      currentDate: today.toISOString(),
      cutoffDate: oldestValidDate.toISOString(),
      daysAgo: 90
    },
    context: "forceCleanUpExpiredTokens"
  });

  // Count total users with tokens
  const totalUsersWithTokens = await Users.find({
    "services.resume.loginTokens.0": { $exists: true }
  }).countAsync();

  // Count tokens that should be removed (older than 90 days)
  const usersWithOldTokens = await Users.find({
    $or: [
      { "services.resume.loginTokens.when": { $lt: oldestValidDate } },
      { "services.resume.loginTokens.when": { $lt: oldestValidDateTimestamp } }
    ]
  }).countAsync();

  // Count tokens that should be kept (newer than 90 days)
  const usersWithValidTokens = totalUsersWithTokens - usersWithOldTokens;

  ninjalog.log({
    msg: "Token cleanup statistics",
    val: {
      totalUsersWithTokens,
      usersWithOldTokens,
      usersWithValidTokens
    },
    context: "forceCleanUpExpiredTokens"
  });

  // Safety check: If we're about to delete more than 50% of tokens, abort
  if (totalUsersWithTokens > 0 && usersWithOldTokens > totalUsersWithTokens * 0.5) {
    ninjalog.error({
      msg: "SAFETY CHECK FAILED: Cleanup would remove more than 50% of all tokens!",
      val: {
        wouldRemove: usersWithOldTokens,
        totalTokens: totalUsersWithTokens,
        percentage: Math.round((usersWithOldTokens / totalUsersWithTokens) * 100)
      },
      context: "forceCleanUpExpiredTokens"
    });
    ninjalog.error({
      msg: "ABORTING CLEANUP to prevent mass logout. Please investigate why so many tokens appear to be old",
      context: "forceCleanUpExpiredTokens"
    });
    return;
  }

  // If no old tokens to remove, skip the update
  if (usersWithOldTokens === 0) {
    ninjalog.log({
      msg: "No expired tokens found. Skipping cleanup.",
      context: "forceCleanUpExpiredTokens"
    });
    ninjalog.log({
      msg: "========================================",
      context: "forceCleanUpExpiredTokens"
    });
    return;
  }

  // Proceed with cleanup
  ninjalog.log({
    msg: "Proceeding with token cleanup...",
    context: "forceCleanUpExpiredTokens"
  });
  const lastModified = await getTimestampInfo("autorun_script", null, "forceCleanUpExpiredTokens");

  const result = await Users.updateAsync(
    {
      $or: [
        { "services.resume.loginTokens.when": { $lt: oldestValidDate } },
        { "services.resume.loginTokens.when": { $lt: oldestValidDateTimestamp } }
      ]
    },
    {
      $pull: {
        "services.resume.loginTokens": {
          $or: [{ when: { $lt: oldestValidDate } }, { when: { $lt: oldestValidDateTimestamp } }]
        }
      },
      $set: { "profile.lastModified": lastModified }
    },
    { multi: true }
  );

  ninjalog.log({
    msg: "Token cleanup complete",
    val: {
      modifiedCount: result.modifiedCount || 0
    },
    context: "forceCleanUpExpiredTokens"
  });
  ninjalog.log({
    msg: "========================================",
    context: "forceCleanUpExpiredTokens"
  });
}

async function fixOnboardingStatus() {
  const query = {
    "profile.onboarded": false,
    $or: [
      { "loginData.loginCount": { $gte: 1 } },
      { "services.azureAdB2c.issuerEmailVerified": true },
      { "services.password.bcrypt": { $exists: true } }
    ]
  };
  const usersToOnboard = await Users.find(query).fetchAsync();

  if (!usersToOnboard.length) {
    return;
  }

  console.log("Fixing user on-boarding:");
  usersToOnboard.forEach(u =>
    console.log(`${u.emails?.[0]?.address} - ${u.profile?.name?.last}, ${u.profile?.name?.first} - ${u._id}`)
  );

  const lastModified = await getTimestampInfo("autorun_script", null, "fixOnboardingStatus");
  await Users.updateAsync(
    query,
    {
      $set: { "profile.onboarded": true, "profile.lastModified": lastModified }
    },
    { multi: true, upsert: false }
  );
}

async function updateAssessmentsWithIncrementalRehearsal() {
  const incrementalRehearsalByAssessmentMeasures = {
    // Sums to 20
    "23": {
      measure: "50",
      name: "Subtraction 0-9",
      grades: ["02", "03", "04", "05"]
    },
    // 2 Digit Addition With and Without Regrouping
    "159": {
      measure: "50",
      name: "Subtraction 0-9",
      grades: ["06", "07", "08"]
    },
    // 2 Digit Subtraction With and Without Regrouping
    "160": {
      measure: "50",
      name: "Subtraction 0-9",
      grades: ["06", "07", "08"]
    },
    // Subtraction 0-9
    "50": {
      measure: "50",
      name: "Subtraction 0-9",
      grades: ["02", "03", "04", "05"]
    },
    // Subtraction 0-12
    "32": {
      measure: "50",
      name: "Subtraction 0-9",
      grades: ["02"]
    },
    // Subtraction 0-15
    "33": {
      measure: "50",
      name: "Subtraction 0-9",
      grades: ["02"]
    },
    // Subtraction 0-20
    "24": {
      measure: "50",
      name: "Subtraction 0-9",
      grades: ["02"]
    },
    // Fact Families: Add/Subtract 0-9
    "51": {
      measure: "50",
      name: "Subtraction 0-9",
      grades: ["03"]
    },
    // Addition 3-Digit Numbers with & without Regrouping
    "26": {
      measure: "50",
      name: "Subtraction 0-9",
      grades: ["04", "05"]
    },
    // Multiplication 0-9
    "41": {
      measure: "42",
      name: "Division 0-9",
      grades: ["03"],
      maxVideos: 46,
      maxSlides: 15
    },
    // Multiplication 5-9
    "68": {
      measure: "42",
      name: "Division 0-9",
      grades: ["03"],
      maxVideos: 46,
      maxSlides: 15
    },
    // Division 0-9
    "42": {
      measure: "42",
      name: "Division 0-9",
      grades: ["03"],
      maxVideos: 46,
      maxSlides: 15
    },
    // Multiplication 0-12
    "69": {
      measure: "42",
      name: "Division 0-9",
      grades: ["04", "05"],
      maxVideos: 46,
      maxSlides: 15
    },
    // Division 0-12
    "72": {
      measure: "42",
      name: "Division 0-9",
      grades: ["04", "05"],
      maxVideos: 46,
      maxSlides: 15
    }
  };
  const assessmentMeasuresToUpdate = Object.keys(incrementalRehearsalByAssessmentMeasures);
  const areMaterialsSet =
    (
      await Assessments.find({
        monitorAssessmentMeasure: { $in: assessmentMeasuresToUpdate },
        ir: { $exists: true }
      }).fetchAsync()
    ).length === assessmentMeasuresToUpdate.length;
  if (areMaterialsSet) {
    return;
  }
  console.log(`Updating assessments: ${assessmentMeasuresToUpdate.join(", ")} with Incremental Rehearsal materials...`);

  Object.entries(incrementalRehearsalByAssessmentMeasures).forEach(async ([assessmentMeasure, ir]) => {
    await Assessments.updateAsync(
      { monitorAssessmentMeasure: assessmentMeasure },
      { $set: { ir } },
      { multi: false, upsert: false }
    );
  });
}

async function fixMissingMessageInStudentHistory() {
  const schoolYear = 2024;
  const openAssessmentResults = await AssessmentResults.find(
    { schoolYear: { $gte: schoolYear }, type: "individual", status: "OPEN" },
    { fields: { studentId: 1, created: 1, benchmarkPeriodId: 1, individualSkills: 1 } }
  ).fetchAsync();
  const query = {
    $or: [
      {
        history: {
          $elemMatch: {
            message: { $exists: false }
          }
        }
      },
      {
        _id: { $in: openAssessmentResults.map(s => s.studentId) },
        currentSkill: { $exists: false },
        "history.0": { $exists: true }
      }
    ]
  };
  const potentiallyAffectedStudents = await Students.find(query, {
    fields: { history: 1, currentSkill: 1 }
  }).fetchAsync();
  const assessmentResultByStudentId = keyBy(openAssessmentResults, "studentId");
  const studentGroupEnrollments = await StudentGroupEnrollments.find(
    { studentId: { $in: potentiallyAffectedStudents.map(s => s._id) }, isActive: true },
    { fields: { studentId: 1 } }
  ).fetchAsync();
  const studentGroupEnrollmentsByStudentId = keyBy(studentGroupEnrollments, "studentId");
  const students = potentiallyAffectedStudents.filter(s => studentGroupEnrollmentsByStudentId[s._id]);
  const shouldRun = students.length;
  if (!shouldRun) {
    return;
  }
  console.log(
    "Restoring message field to students history or fixed intervention state, Affected students: ",
    students.length
  );
  for await (const student of students.filter(s => studentGroupEnrollmentsByStudentId[s._id])) {
    let shouldFixCurrentSkill = false;
    const assessmentResult = assessmentResultByStudentId[student._id];
    if (assessmentResult && !Object.keys(student.currentSkill || {}).length) {
      shouldFixCurrentSkill = true;
    }
    // NOTE(fmazur) - history.slice(history.length - index) is history without most recent item
    // NOTE(fmazur) - history.slice(history.length - index - 1)[0] is one item ahead that should be used for current skill
    const historyLength = student.history.length;
    student.history.forEach((item, index) => {
      const currentSkillHistoryItem = student.history.slice(historyLength - index - 1)?.[0];
      const currentSkill = {
        benchmarkAssessmentId: currentSkillHistoryItem.benchmarkAssessmentId,
        benchmarkAssessmentName: currentSkillHistoryItem.benchmarkAssessmentName,
        benchmarkAssessmentTargets: currentSkillHistoryItem.benchmarkAssessmentTargets,
        assessmentId: currentSkillHistoryItem.assessmentId,
        assessmentName: currentSkillHistoryItem.assessmentName,
        assessmentTargets: currentSkillHistoryItem.assessmentTargets,
        interventions: currentSkillHistoryItem.interventions,
        assessmentResultId: currentSkillHistoryItem.assessmentResultId,
        benchmarkPeriodId: currentSkillHistoryItem.benchmarkPeriodId
      };
      const historyToGenerateMessageFrom = student.history.slice(historyLength - index) || [];
      const message = getIndividualResultsMessage({
        history: historyToGenerateMessageFrom,
        currentSkill
      });

      if (currentSkillHistoryItem && index !== 0 && !currentSkillHistoryItem.message?.messageCode) {
        currentSkillHistoryItem.message = message;
      } else if (index === 0 && !student.history[historyLength - 1]?.message?.messageCode) {
        const oldestHistoryItem = student.history[historyLength - 1];
        if (historyLength === 1 && !student.currentSkill?.assessmentId && student.currentSkill?.message?.messageCode) {
          oldestHistoryItem.message = student.currentSkill.message;
        } else if (student.currentSkill?.benchmarkPeriodId && !student.currentSkill?.assessmentId) {
          oldestHistoryItem.message = { messageCode: "56", dismissed: false };
        } else if (
          student.history[historyLength - 2] &&
          oldestHistoryItem.benchmarkAssessmentId !== student.history[historyLength - 2].benchmarkAssessmentId &&
          !oldestHistoryItem.interventions?.length &&
          !student.history[historyLength - 2]?.interventions?.length &&
          oldestHistoryItem.benchmarkPeriodId === student.history[historyLength - 2]?.benchmarkPeriodId
        ) {
          oldestHistoryItem.message = { messageCode: "55", dismissed: false };
        } else if (oldestHistoryItem.interventions?.length) {
          oldestHistoryItem.message = { messageCode: "58", dismissed: false };
        } else {
          oldestHistoryItem.message = { messageCode: "51", dismissed: false };
        }
      }
    });
    const lastMessage = getIndividualResultsMessage({
      history: student.history.slice(1),
      currentSkill: student.history[0]
    });

    if (lastMessage && !student.history[0].message?.messageCode) {
      // if (lastMessage) {
      student.history[0].message =
        !student.currentSkill?.assessmentId && student.currentSkill?.message?.messageCode
          ? student.currentSkill.message
          : lastMessage;
    }
    let currentSkill;
    if (shouldFixCurrentSkill) {
      currentSkill = {
        benchmarkAssessmentId: assessmentResult.individualSkills.benchmarkAssessmentId,
        benchmarkAssessmentName: assessmentResult.individualSkills.benchmarkAssessmentName,
        benchmarkAssessmentTargets: assessmentResult.individualSkills.benchmarkAssessmentTargets,
        assessmentId: assessmentResult.individualSkills.assessmentId,
        assessmentName: assessmentResult.individualSkills.assessmentName,
        assessmentTargets: assessmentResult.individualSkills.assessmentTargets,
        interventions: assessmentResult.individualSkills.interventions,
        assessmentResultId: assessmentResult.individualSkills.assessmentResultId,
        whenStarted: {
          by: "server_autorun",
          on: assessmentResult.created.on,
          date: assessmentResult.created.date
        },
        benchmarkPeriodId: assessmentResult.benchmarkPeriodId
      };
      currentSkill.message = getIndividualResultsMessage({
        currentSkill,
        history: student.history
      });
    }
    console.log(`StudentId: ${student._id}${shouldFixCurrentSkill ? ", Had fixed currentSkill" : ""}`);
    await Students.updateAsync(
      { _id: student._id },
      { $set: { history: student.history, ...(shouldFixCurrentSkill ? { currentSkill } : {}) } }
    );
  }
}

async function trimRosterImportsAndRosterImportItemsCollections() {
  console.log("Trimming RosterImports and RosterImportItems collections...");
  const orgsWithImportsToRemove = await RosterImports.aggregate([
    { $project: { orgid: 1 } },
    { $group: { _id: "$orgid", totalImports: { $sum: 1 } } },
    { $match: { totalImports: { $gt: ROSTER_IMPORTS_LIMIT } } }
  ]);

  for await (const org of orgsWithImportsToRemove) {
    await trimRosterImportsAndRosterImportItemsCollectionsForOrg(org._id);
  }
}

async function setUserIdentifiersForOneRosterOrgs() {
  const query = {
    rostering: "rosterOR",
    "rosteringSettings.userIdentifiers": { $exists: false }
  };
  const needsUpdating = await Organizations.findOneAsync(query);
  if (!needsUpdating) {
    return;
  }
  console.log("Setting userIdentifiers for OneRoster Organizations...");
  const oneRosterOrgs = await Organizations.find(query).fetchAsync();
  for await (const org of oneRosterOrgs) {
    await Organizations.updateAsync(
      { _id: org._id },
      { $set: { "rosteringSettings.userIdentifiers": { teacher: ["administrator", "aide"] } } }
    );
  }
}

// function migrateUserFields() {
//   const query = {
//     lastPasswordChange: { $exists: true }
//   };
//   const usersToMigrate = Users.find(query).fetch();
//
//   if (!usersToMigrate.length) {
//     return;
//   }
//
//   console.log("Migrating user fields..");
//   usersToMigrate.forEach(user =>
//     Users.update(
//       { _id: user._id },
//       {
//         $set: { "profile.lastPasswordChange": user.lastPasswordChange },
//         $unset: { lastPasswordChange: "" }
//       }
//     )
//   );
// }

// NOTE(fmazur) - Temporary autorun
// setCustomTime("Fall");
// migrateOrganizationField();
// migrateFileUpload();
// setRosteringMessage();
// extendGroupedAssessments();
// cleanUpCollections();
// addDownloaderRole();
// updateGroupedAssessmentsWithDifficultyOrder();
// fixMissingMessageInStudentHistory();

// NOTE(fmazur) - Should always run
await setRosteringThreshold();
await updateAssessmentsWithIncrementalRehearsal();
await updateAssessmentGrowthCollection();
await syncInterventionProtocols();
await forceCleanUpExpiredTokens();
await fixOnboardingStatus();
await updateAvailableVideoList();
await trimRosterImportsAndRosterImportItemsCollections();
await setUserIdentifiersForOneRosterOrgs();
// migrateUserFields();
