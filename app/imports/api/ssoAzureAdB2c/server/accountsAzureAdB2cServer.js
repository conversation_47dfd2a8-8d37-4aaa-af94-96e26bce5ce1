/* Listen to calls to `login-with-sso`. This is where
 users actually get logged in to meteor via openIdConnect.  The current selector
 looks for a match on email or identity providers unquie id. Then confirms user's org matches the org it is being issued from in SSO via ssoIssuerOrgIds.
 {
 type: "azureAdB2c",
 userId: "CrQmEg2MBHaL65bHu"
 }

 */
import _ from "lodash";
import { retrieveCredential } from "./azureAdB2cServer";
import { Organizations } from "../../organizations/organizations";
import { ninjalog } from "../../utilities/utilities";
import { updateLoginData } from "../../users/server/methods";
import { getTimestampInfo } from "../../helpers/getTimestampInfo";

// eslint-disable-next-line max-statements
Accounts.registerLoginHandler(async options => {
  if (!options.openIdConnect) {
    return undefined;
  } // don't handle

  const context = "HandleSSOLoginAttempt";

  ninjalog.log({ msg: "Login attempt begin", context });

  console.log("[<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>] Received login request with:", {
    hasOpenIdConnect: !!options.openIdConnect,
    tokenPrefix: `${options.openIdConnect?.credentialToken?.substring(0, 10)}...`,
    secretPrefix: `${options.openIdConnect?.credentialSecret?.substring(0, 10)}...`,
    tokenLength: options.openIdConnect?.credentialToken?.length,
    secretLength: options.openIdConnect?.credentialSecret?.length
  });

  const result = await retrieveCredential(
    options.openIdConnect.credentialToken,
    options.openIdConnect.credentialSecret
  );
  ninjalog.trace({ msg: "Retrieve credential call complete ", val: result, context });

  console.log("[SSO-LoginHandler] Retrieved credential result:", {
    hasResult: !!result,
    hasServiceData: !!(result && result.serviceData),
    serviceName: result?.serviceName,
    serviceDataKeys: result?.serviceData ? Object.keys(result.serviceData) : []
  });

  if (!result) {
    return throwError({
      code: 401,
      message: "No matching login attempt found, please confirm popups are enabled and try again.",
      details: ""
    });
  }

  if (result.serviceData) {
    console.log("[SSO-LoginHandler] ServiceData contents:", {
      email: result.serviceData.email,
      name: result.serviceData.name,
      sub: result.serviceData.sub,
      issuerUserId: result.serviceData.issuerUserId,
      issuerOrgId: result.serviceData.issuerOrgId,
      issuerEmailVerified: result.serviceData.issuerEmailVerified,
      hasIdentity: !!result.serviceData.identity
    });

    if (result.serviceData.identity) {
      console.log("[SSO-LoginHandler] Identity object keys:", Object.keys(result.serviceData.identity));
    }
  }

  let user;
  let org;
  const { email, issuerUserId, issuerOrgId, issuerEmailVerified, issuerLogoutRedirect, name, sub } = result.serviceData
    ? result.serviceData
    : {};

  const userDetailsString = `User details: email: ${email}\nname: ${name}\nsub: ${sub}\nissuerUserId: ${issuerUserId}\nissuerOrgId: ${issuerOrgId}`;

  console.log("[SSO-LoginHandler] Extracted user details:", {
    email,
    name,
    sub,
    issuerUserId,
    issuerOrgId,
    issuerEmailVerified,
    issuerLogoutRedirect
  });

  if (issuerOrgId) {
    const organization = await Organizations.findOneAsync({ ssoIssuerOrgId: issuerOrgId });
    if (organization) {
      org = organization;
    }
  }

  if (!org) {
    return throwError({
      code: 403,
      message: "No matching organization for user was found",
      details: userDetailsString
    });
  }

  const dataAdmins = (
    await Meteor.users
      .find({ "profile.orgid": org._id, "profile.siteAccess.role": "arbitraryIddataAdmin" })
      .fetchAsync()
  ).map(dataAdmin => {
    const { first, middle, last } = dataAdmin.profile.name;
    const fullName = `${first}${!middle ? "" : ` ${middle}`} ${last}`;
    return {
      fullName,
      email: dataAdmin.emails[0].address
    };
  });
  // TODO(fmazur) - display different message if district does not have any data admins

  if (issuerUserId) {
    // first attempt find by local id.
    user = await Meteor.users.findOneAsync({ "profile.orgid": org._id, "profile.localId": issuerUserId });
    if (!user) {
      // second attempt find by issuer user id
      user = await Meteor.users.findOneAsync({
        "profile.orgid": org._id,
        "services.azureAdB2c.issuerUserId": issuerUserId
      });
    }
  }

  if (!user && email) {
    user = await Meteor.users.findOneAsync({ "emails.address": email });

    if (user) {
      // if matching on email confirm it is verified from SSO
      if (!issuerEmailVerified) {
        return throwError({
          code: 403,
          message: "Email is not verified",
          details: JSON.stringify({ userDetailsString, dataAdmins })
        });
      }
    }
  }

  if (!user) {
    return throwError({
      code: 401,
      message: "No matching user found",
      details: JSON.stringify({ userDetailsString, dataAdmins })
    });
  }

  const setAttrs = {};
  _.each(result.serviceData, (value, key) => {
    setAttrs[`services.${result.serviceName}.${key}`] = value;
  });

  if (user.profile) {
    if (!user.profile.onboarded) {
      setAttrs[`profile.onboarded`] = true;
    }
  }
  if (issuerLogoutRedirect) {
    setAttrs[`profile.ssoIssuerLogoutRedirect`] = issuerLogoutRedirect;
  }

  if (user.profile.ssoIssuerLogoutRedirect !== issuerLogoutRedirect) {
    setAttrs[`profile.lastModified`] = await getTimestampInfo(user._id || "registerLoginHandler", org._id);
  }

  // Track authentication method for password expiry logic
  setAttrs["profile.lastAuthMethod"] = "sso";

  await Meteor.users.updateAsync(user._id, { $set: setAttrs });
  const retVal = { type: result.serviceName, userId: user._id };
  ninjalog.trace({ msg: "User Found", val: retVal, context });
  return retVal;
});

async function throwError({ code, message, details, context = "HandleSSOLoginAttempt", email }) {
  await updateLoginData(email);
  ninjalog.error({ msg: `${code} ${message}`, val: details, context });
  return {
    type: "azureAdB2c",
    error: new Meteor.Error(code, message, details)
  };
}
