import { Meteor } from "meteor/meteor";
import moment from "moment";
import { check, Match } from "meteor/check";
import { uniqBy } from "lodash";
import { Students } from "../students";
import { StudentGroupEnrollments } from "../../studentGroupEnrollments/studentGroupEnrollments";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { Users } from "../../users/users";
import * as auth from "../../authorization/server/methods";
import { getCurrentSchoolYear, getMeteorUser, getMeteorUserId, getValuesByKey } from "../../utilities/utilities";
import { Organizations } from "../../organizations/organizations";
import { getTimestampInfo } from "../../helpers/getTimestampInfo";

export async function insert(studentDoc) {
  Students.validate(studentDoc);
  await Students.insertAsync(studentDoc);
}

async function getNumberOfActiveStudents(orgid, schoolYear = null) {
  const user = await getMeteorUser();
  const siteAccess = auth.getSiteAccess(user);
  let query = { isActive: true, schoolYear: schoolYear || (await getCurrentSchoolYear(user, orgid)) };
  let groupBy = "$orgid";
  let orgids = [];

  if (orgid) {
    query.orgid = orgid;
    groupBy = "$siteId";
  } else if (auth.hasRoleInSiteAccess(siteAccess, "support")) {
    orgids = auth.getSupportUserAccess(user);
  } else if (auth.hasRoleInSiteAccess(siteAccess, "universalCoach")) {
    orgids = (await Organizations.find({}, { _id: 1 }).fetchAsync()).map(org => org._id);
  }

  if (orgids.length) {
    const orConditions = await Promise.all(
      orgids.map(async id => ({
        isActive: true,
        orgid: id,
        schoolYear: schoolYear || (await getCurrentSchoolYear(user, id))
      }))
    );
    query = { $or: orConditions };
  }
  return StudentGroupEnrollments.aggregate([
    {
      $match: query
    },
    {
      $group: {
        _id: groupBy,
        totalStudents: { $sum: 1 }
      }
    }
  ]);
}

async function getStudentDemographicData(studentIdQuery) {
  const { orgid, siteId, schoolYear } = studentIdQuery;
  
  // Get student count
  const studentIds = (await StudentGroupEnrollments.find(studentIdQuery).fetchAsync()).map(stu => stu.studentId);
  const numStudents = await Students.find({ _id: { $in: studentIds } }).countAsync();
  
  // Get teacher count (teachers assigned to this site)
  const siteTeachers = await Users.find({
    "profile.orgid": orgid,
    "profile.siteAccess": {
      $elemMatch: {
        siteId: siteId,
        role: "arbitraryIdteacher",
        isActive: true
      }
    }
  }).fetchAsync();
  const teachersOnboarded = siteTeachers.filter(t => t.profile.onboarded).length;
  
  // Get student groups (classes) count
  const studentGroups = await StudentGroups.find({
    siteId,
    orgid,
    schoolYear,
    isActive: true
  }).fetchAsync();
  const numClasses = studentGroups.length;
  
  // Get unique grades from student groups
  const uniqueGrades = [...new Set(studentGroups.map(sg => sg.grade))].filter(g => g);
  
  // Sort grades properly (K first, then numeric)
  const sortedGrades = uniqueGrades.sort((a, b) => {
    // Handle K/Kindergarten
    if (a === 'K' || a === 'Kindergarten') return -1;
    if (b === 'K' || b === 'Kindergarten') return 1;
    
    // Handle numeric grades
    const numA = parseInt(a);
    const numB = parseInt(b);
    
    if (!isNaN(numA) && !isNaN(numB)) {
      return numA - numB;
    }
    
    // Fallback to string comparison
    return a.localeCompare(b);
  });
  
  const demographics = [
    { label: "Teachers onboarded", value: teachersOnboarded },
    { label: "Students", value: numStudents },
    { label: "Classes/Groups", value: numClasses }
  ];
  
  // Add grades if any exist
  if (sortedGrades.length > 0) {
    const gradeDisplay = sortedGrades.join(', ');
    demographics.push({ label: "Grades", value: gradeDisplay });
  }
  
  return demographics;
}

async function isAllowedToGetNumberOfActiveStudents(userId, orgid) {
  return auth.hasAccess(["support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
    userId,
    orgid,
    shouldCheckOrgAccessForSupport: false
  });
}

export const searchStudents = async ({ orgid, siteIds, firstName, lastName, localId, stateId }) => {
  const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  const keyFirstName = "identity.name.firstName";
  const keyLastName = "identity.name.lastName";
  const keyLocalId = "identity.identification.localId";
  const keyStateId = "identity.identification.stateId";
  const query = { orgid, schoolYear };
  if (firstName) {
    query[keyFirstName] = { $regex: new RegExp(firstName, "i") };
  }
  if (lastName) {
    query[keyLastName] = { $regex: new RegExp(lastName, "i") };
  }
  if (localId) {
    query[keyLocalId] = { $regex: new RegExp(localId, "i") };
  }
  if (stateId) {
    query[keyStateId] = { $regex: new RegExp(stateId, "i") };
  }
  const students = await Students.find(query, {
    fields: { grade: 1, studentGrade: 1, identity: 1, "demographic.birthDate": 1, orgid: 1, schoolYear: 1, siteId: 1 }
  }).fetchAsync();
  const studentIds = students.map(s => s._id);
  const studentEnrollments = await StudentGroupEnrollments.find(
    {
      studentId: { $in: studentIds },
      siteId: { $in: siteIds },
      schoolYear
    },
    { sort: { "created.on": -1 } }
  ).fetchAsync();

  // should return the first SE for each student found in a pre-sorted list
  const currentEnrollmentForEachStudent = uniqBy(studentEnrollments, se => se.studentId);

  const studentGroupIds = currentEnrollmentForEachStudent.map(group => group.studentGroupId);
  const studentGroups = await StudentGroups.find({
    _id: { $in: studentGroupIds }
  }).fetchAsync();

  return { students, studentEnrollments: currentEnrollmentForEachStudent, studentGroups };
};

Meteor.methods({
  async "Students:getNumberOfActiveStudents"(orgid = null, schoolYear = null) {
    check(orgid, Match.Maybe(String));
    check(schoolYear, Match.Maybe(Number));
    if (!(await isAllowedToGetNumberOfActiveStudents(this.userId, orgid))) {
      throw new Meteor.Error(403, "Invalid permissions to call method");
    }
    return getNumberOfActiveStudents(orgid, schoolYear);
  },
  async "Students:GetStudentDemographicsAtSite"(siteId, orgid, schoolYear) {
    check(siteId, String);
    check(orgid, String);
    check(schoolYear, Number);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      // added universalDataAdmin
      !(await auth.hasAccess(["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        siteId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "Invalid permissions to call method");
    }

    const studentIdQuery = {
      orgid,
      siteId,
      schoolYear,
      isActive: true
    };
    return getStudentDemographicData(studentIdQuery);
  },
  async "Students:GetStudentDemographicsForGradeAtSite"(siteId, orgid, grade, schoolYear) {
    check(siteId, String);
    check(orgid, String);
    check(grade, String);
    check(schoolYear, Number);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["admin", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        siteId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "Invalid permissions to call method");
    }
    const studentIdQuery = {
      siteId,
      orgid,
      grade,
      schoolYear,
      isActive: true
    };
    return getStudentDemographicData(studentIdQuery);
  },
  async "Students:updateCalculateAsOfDate"({ orgid, siteId, entityId, calculateStatsAsOfDate, benchmarkPeriodId }) {
    check(orgid, String);
    check(siteId, String);
    check(entityId, String);
    check(calculateStatsAsOfDate, Date);
    check(benchmarkPeriodId, String);
    if (
      !this.userId ||
      !(await auth.hasAccess(["admin", "universalCoach"], {
        userId: this.userId,
        siteId
      }))
    ) {
      throw new Meteor.Error(403, "Insufficient rights to perform method!");
    }

    const freezeStartDate = moment(
      `15.05.${await getCurrentSchoolYear(await getMeteorUser(), orgid)} 12:00:00`,
      "DD.MM.YYYY hh:mm:ss"
    ).toDate();
    const dateToSet =
      calculateStatsAsOfDate.valueOf() < freezeStartDate.valueOf() ? calculateStatsAsOfDate : freezeStartDate;

    const individualStatsAsOfDateUpdate = Students.find({ _id: entityId }).indvidualStatsAsOfDate || {};
    individualStatsAsOfDateUpdate[benchmarkPeriodId] = dateToSet;
    const lastModified = await getTimestampInfo(getMeteorUserId(), orgid, "Students:updateCalculateAsOfDate");
    await Students.updateAsync(entityId, {
      $set: { individualStatsAsOfDate: individualStatsAsOfDateUpdate, lastModified }
    });
  },
  async "Students:searchStudents"({ orgid, siteIds, firstName, lastName, localId, stateId }) {
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "Insufficient rights to perform method!");
    }
    check(orgid, String);
    check(siteIds, Array);
    check(firstName, String);
    check(lastName, String);
    check(localId, String);
    check(stateId, String);

    return searchStudents({ orgid, siteIds, firstName, lastName, localId, stateId });
  },
  async "Students:getStudents"(studentGroupIds, schoolYear) {
    check(studentGroupIds, Array);
    check(schoolYear, Number);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    const studentGroupEnrollments = await StudentGroupEnrollments.find(
      { studentGroupId: { $in: studentGroupIds }, schoolYear, isActive: true },
      {
        fields: {
          studentId: 1,
          studentGroupId: 1
        }
      }
    ).fetchAsync();

    const studentIds = studentGroupEnrollments?.map(sge => sge.studentId) || [];
    const studentGroupByStudentId = getValuesByKey(studentGroupEnrollments, "studentId", "studentGroupId");

    const students = await Students.find(
      { _id: { $in: studentIds }, schoolYear },
      {
        fields: {
          schoolYear: 1,
          grade: 1,
          identity: 1,
          demographic: 1,
          currentSkill: 1,
          history: 1
        }
      }
    ).fetchAsync();

    return students?.map(s => ({ ...s, studentGroupId: studentGroupByStudentId[s._id] }));
  }
});
