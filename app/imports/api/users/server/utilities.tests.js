import moment from "moment";
import { Meteor } from "meteor/meteor";
import { getUpdatedCoachProfile, shouldForceUserToChangePassword } from "./utilities";
import { Organizations } from "/imports/api/organizations/organizations";

// Mock the Organizations collection
jest.mock("/imports/api/organizations/organizations");

describe("shouldForceUserToChangePassword", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock Meteor.settings
    Meteor.settings = {
      public: {
        FORCE_CHANGE_PASSWORD_INTERVAL_IN_DAYS: 150,
        FORCE_CHANGE_PASSWORD_INTERVAL_WITH_MFA_IN_DAYS: 365
      }
    };
  });

  describe("SSO-only organizations", () => {
    it("should return false for users in SSO-only organizations", async () => {
      const user = {
        profile: {
          orgid: "testOrg",
          lastPasswordChange: moment().subtract(200, "days").valueOf(),
          lastAuthMethod: "password"
        },
        services: {
          password: { bcrypt: "hash" }
        }
      };

      Organizations.findOneAsync.mockResolvedValue({
        isMFARequired: false,
        useSSOOnly: true
      });

      const result = await shouldForceUserToChangePassword(user);
      expect(result).toBe(false);
    });
  });

  describe("Authentication method tracking", () => {
    it("should return false for users who last authenticated via SSO", async () => {
      const user = {
        profile: {
          orgid: "testOrg",
          lastPasswordChange: moment().subtract(200, "days").valueOf(),
          lastAuthMethod: "sso"
        },
        services: {
          password: { bcrypt: "hash" }
        }
      };

      Organizations.findOneAsync.mockResolvedValue({
        isMFARequired: false,
        useSSOOnly: false
      });

      const result = await shouldForceUserToChangePassword(user);
      expect(result).toBe(false);
    });

    it("should check password expiry for users who last authenticated via password", async () => {
      const user = {
        profile: {
          orgid: "testOrg",
          lastPasswordChange: moment().subtract(200, "days").valueOf(),
          lastAuthMethod: "password"
        },
        services: {
          password: { bcrypt: "hash" }
        }
      };

      Organizations.findOneAsync.mockResolvedValue({
        isMFARequired: false,
        useSSOOnly: false
      });

      const result = await shouldForceUserToChangePassword(user);
      expect(result).toBe(true); // 200 days > 150 days threshold
    });

    it("should check password expiry when lastAuthMethod is undefined (legacy users)", async () => {
      const user = {
        profile: {
          orgid: "testOrg",
          lastPasswordChange: moment().subtract(200, "days").valueOf()
          // lastAuthMethod is undefined
        },
        services: {
          password: { bcrypt: "hash" }
        }
      };

      Organizations.findOneAsync.mockResolvedValue({
        isMFARequired: false,
        useSSOOnly: false
      });

      const result = await shouldForceUserToChangePassword(user);
      expect(result).toBe(true); // Should check password expiry for legacy users
    });
  });

  describe("Password expiry intervals", () => {
    it("should use 150-day interval for non-MFA organizations", async () => {
      const user = {
        profile: {
          orgid: "testOrg",
          lastPasswordChange: moment().subtract(149, "days").valueOf(),
          lastAuthMethod: "password"
        },
        services: {
          password: { bcrypt: "hash" }
        }
      };

      Organizations.findOneAsync.mockResolvedValue({
        isMFARequired: false,
        useSSOOnly: false
      });

      const resultNotExpired = await shouldForceUserToChangePassword(user);
      expect(resultNotExpired).toBe(false); // 149 days < 150 days

      // Test with expired password
      user.profile.lastPasswordChange = moment().subtract(151, "days").valueOf();
      const resultExpired = await shouldForceUserToChangePassword(user);
      expect(resultExpired).toBe(true); // 151 days > 150 days
    });

    it("should use 365-day interval for MFA-required organizations", async () => {
      const user = {
        profile: {
          orgid: "testOrg",
          lastPasswordChange: moment().subtract(364, "days").valueOf(),
          lastAuthMethod: "password"
        },
        services: {
          password: { bcrypt: "hash" }
        }
      };

      Organizations.findOneAsync.mockResolvedValue({
        isMFARequired: true,
        useSSOOnly: false
      });

      const resultNotExpired = await shouldForceUserToChangePassword(user);
      expect(resultNotExpired).toBe(false); // 364 days < 365 days

      // Test with expired password
      user.profile.lastPasswordChange = moment().subtract(366, "days").valueOf();
      const resultExpired = await shouldForceUserToChangePassword(user);
      expect(resultExpired).toBe(true); // 366 days > 365 days
    });
  });

  describe("Edge cases", () => {
    it("should handle missing organization gracefully", async () => {
      const user = {
        profile: {
          orgid: "testOrg",
          lastPasswordChange: moment().subtract(200, "days").valueOf(),
          lastAuthMethod: "password"
        },
        services: {
          password: { bcrypt: "hash" }
        }
      };

      Organizations.findOneAsync.mockResolvedValue(null);

      const result = await shouldForceUserToChangePassword(user);
      expect(result).toBe(true); // Falls back to default behavior
    });

    it("should handle missing profile fields gracefully", async () => {
      const user = {
        profile: {
          orgid: "testOrg",
          lastPasswordChange: moment().subtract(200, "days").valueOf()
          // lastAuthMethod is missing
        },
        services: {
          password: { bcrypt: "hash" }
        }
      };

      Organizations.findOneAsync.mockResolvedValue({
        isMFARequired: false,
        useSSOOnly: false
      });

      const result = await shouldForceUserToChangePassword(user);
      expect(result).toBe(true); // Should still check password expiry
    });
  });
});

describe("getUpdatedCoachProfile", () => {
  it("should add missing site access and localId to coach profile", () => {
    const currentProfile = {
      orgid: "orgid",
      siteAccess: [
        {
          role: "arbitraryIdadmin",
          siteId: "siteId",
          schoolYear: 2018,
          isActive: true
        }
      ]
    };
    const updatedUser = {
      siteIds: ["siteId", "otherSite"],
      localId: "newLocalId",
      role: "arbitraryIdadmin",
      isActive: true
    };
    const currentSchoolYear = 2019;

    const result = getUpdatedCoachProfile(updatedUser, currentProfile, currentSchoolYear, {});

    expect(result).toMatchObject({
      orgid: "orgid",
      localId: "newLocalId",
      siteAccess: [
        {
          role: "arbitraryIdadmin",
          siteId: "siteId",
          schoolYear: 2019,
          isActive: true
        },
        {
          role: "arbitraryIdadmin",
          siteId: "otherSite",
          schoolYear: 2019,
          isActive: true
        },
        {
          role: "arbitraryIdadmin",
          siteId: "siteId",
          schoolYear: 2018,
          isActive: true
        }
      ],
      lastModified: {}
    });
  });
});
