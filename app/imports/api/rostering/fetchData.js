import { difference, get, groupBy, keyBy, orderBy, uniq, uniqBy } from "lodash";
import promiseRetry from "promise-retry";
import { Meteor } from "meteor/meteor";

import { getRosteringTypeLabel, getSchoolNumberComparison } from "/imports/ui/utilities";
import { getExternalRosteringClassDetails } from "./getExternalRosteringClassDetails";
import {
  getClassSectionIdForOneRoster,
  getCurrentSchoolYear,
  getGradeFromDescriptor,
  getGradeLabel,
  getGradesFromOneRosterStudents,
  getGradeTranslationsByGrade,
  getSectionGradeMajority,
  getMeteorUserId,
  mapExternalRosteringStudentGrades,
  normalizeExternalRosteringId,
  getGradeByGradeTranslation,
  studentsInMultipleClassesInfo,
  fetchRosteringType
} from "../utilities/utilities";
import { validateAndSetSubtotals } from "../../ui/pages/data-admin/upload/validation-helpers";
// eslint-disable-next-line import/no-cycle
import {
  failRosterImport,
  generateRosterImportsObject,
  insertRoster,
  insertRosterImportDocAndTrimCollection
} from "../rosterImports/methods";
import { RosterImports } from "../rosterImports/rosterImports";
// eslint-disable-next-line import/no-cycle
import { sendEmailForRosterImportErrors } from "../utilities/methods";
import { getFormattedStudentGroupErrors } from "../../ui/pages/data-admin/upload/file-upload-utils";
import { ExternalRosteringAPIManager } from "../../scripts/externalRosteringAPIManager";
import {
  fetchGradeLevelDescriptors,
  fetchSchoolsResource,
  fetchSectionsResource,
  fetchStaffSectionAssociationsResource,
  fetchStaffsResource,
  fetchStudentSectionAssociationsResource,
  fetchStudentsResource,
  getRosterSettings
} from "./utils";
import { Organizations } from "../organizations/organizations";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { convertSchoolIdsToNCES } from "../sites/methods";
import { Sites } from "../sites/sites";
import { StudentGroups } from "../studentGroups/studentGroups";
import {
  areAnyStudentGroupsUnmatchedInSites,
  areSitesMatching
} from "../../ui/pages/data-admin/roster-filtering/utilities";
import { Users } from "../users/users";
import { Settings } from "../settings/settings";

export async function initRosterDocument(orgid) {
  const rosteringType = await fetchRosteringType(orgid);
  const source = getRosteringTypeLabel(rosteringType);
  const customUserId = `CRON_${source}`;
  const userId = getMeteorUserId(customUserId);
  const rosterImport = {
    ...(await generateRosterImportsObject({ customUserId: userId, orgid })),
    status: "validating",
    source: getRosteringTypeLabel(rosteringType)
  };

  const timestampInfo = await getTimestampInfo(userId, orgid);
  const minuteInMs = 60000;
  const timestampOneMinuteAgo = timestampInfo.on - minuteInMs;
  const query = {
    orgid,
    "started.by": customUserId,
    "started.on": { $gte: timestampOneMinuteAgo }
  };
  const hasOtherImportStartedRecently = !!(await RosterImports.findOneAsync(query));
  if (hasOtherImportStartedRecently) {
    return null;
  }

  return insertRosterImportDocAndTrimCollection(rosterImport);
}

function getRosterImportErrors(rosterImport) {
  const errors = [];

  if (rosterImport.error) {
    if (rosterImport.error.clientMessage) {
      errors.push(rosterImport.error.clientMessage);
    }
    if (rosterImport.error.errorMessage) {
      errors.push(rosterImport.error.errorMessage);
    }
  }

  if (rosterImport.studentGroupErrors) {
    errors.push(getFormattedStudentGroupErrors(rosterImport.studentGroupErrors));
  }
  if (rosterImport.uniqueEmailErrors) {
    errors.push(rosterImport.uniqueEmailErrors.join("\n"));
  }
  if (rosterImport.districtErrors) {
    errors.push(rosterImport.districtErrors.join("\n"));
  }

  return errors;
}

export async function sendEmailForFailedRosterImport(orgid, rosterImportId = "") {
  const rosteringType = await fetchRosteringType(orgid);
  const source = getRosteringTypeLabel(rosteringType);
  const rosterImport = await RosterImports.findOneAsync({ _id: rosterImportId });
  if (!rosterImport) {
    return;
  }

  const importErrors = getRosterImportErrors(rosterImport);

  sendEmailForRosterImportErrors({
    orgid,
    rosterImportType: source,
    importErrors
  });
}

export function handleError({ resourceName, data = [] }) {
  if (!data.length) {
    throw Error(`No data received from endpoint for ${resourceName}`);
  }
}

async function retryFunction(func, description = "") {
  const isLocalEnv = Meteor.settings.public.ENVIRONMENT === "LOCAL";
  const maxNumberOfRetries = isLocalEnv ? 1 : 9;
  // eslint-disable-next-line no-useless-catch
  try {
    return promiseRetry(
      async (retry, number) => {
        try {
          return func();
        } catch (e) {
          if ([400, 401, 403, 404].includes(e.statusCode) && maxNumberOfRetries === number) {
            throw e;
          }
          e.message = `Problem with getting ${description} after ${number} attempts.`;
          retry(e);
        }
        return null;
      },
      { retries: maxNumberOfRetries, minTimeout: 2000 }
    );
  } catch (e) {
    throw e;
  }
}

async function updateTeacherFilters(orgid, teacherIds) {
  Organizations.updateAndLog(
    {
      type: "rosteringFilter",
      timestampInfo: await getTimestampInfo("SyncScheduleScript", orgid),
      findOptions: {
        fields: {
          "rosteringSettings.filters": 1
        }
      },
      orgid
    },
    {
      _id: orgid
    },
    {
      $set: { "rosteringSettings.filters.teachers": teacherIds }
    }
  );
}

export async function getOneRosterImportRowsData(orgid, rostering, existingSites = []) {
  const {
    apiUrl,
    authUrl,
    clientId,
    clientSecret,
    shouldUseScopes,
    shouldUseSequentialRequests = false,
    shouldIgnoreEnrollmentStartDate = false,
    shouldIgnoreEnrollmentEndDate = false,
    limit = 500,
    filters,
    translations,
    userIdentifiers,
    name: orgName
  } = await getRosterSettings(orgid);
  const { allowMultipleGradeLevels = false } = await Organizations.findOneAsync(orgid);
  const endpointParams = {
    apiUrl,
    authUrl,
    clientId,
    clientSecret,
    shouldUseScopes,
    shouldUseSequentialRequests,
    shouldIgnoreEnrollmentStartDate,
    shouldIgnoreEnrollmentEndDate,
    limit
  };
  const { schools: schoolIds, classes: classIds, teachers: teacherIds } = filters;
  const rosteringAPI = await new ExternalRosteringAPIManager({ orgid, ...endpointParams, rosteringType: rostering });

  const schools = await retryFunction(async () => {
    return rosteringAPI.getSchool({ schoolIds });
  }, "schools");
  handleError({ resourceName: "schools", data: schools });

  const enrollments = await retryFunction(async () => {
    return rosteringAPI.getEnrollmentsForSchools({ schoolIds });
  }, "enrollments");
  handleError({ resourceName: "enrollments", data: enrollments });

  const classesDataResponse = await retryFunction(async () => {
    return rosteringAPI.getClassesForSchool({ schoolIds });
  }, "classes");
  const classes = classesDataResponse.filter(c => {
    return classIds.includes(c.sourcedId);
  });
  handleError({ resourceName: "classes", data: classes });

  const teachersWithClassId = await retryFunction(async () => {
    return rosteringAPI.getTeachersForClass({ classIds });
  }, "teachers");

  handleError({ resourceName: "teachers", data: teachersWithClassId.flat(1) });

  const studentsWithClassId = await retryFunction(async () => {
    return rosteringAPI.getStudentsForSchool({
      schoolIds,
      classIds,
      enrollments: enrollments.filter(e => e.role === "student")
    });
  }, "students");

  const studentIds = studentsWithClassId.flat(3).map(s => s.sourcedId);
  let demographicsResponse;
  try {
    demographicsResponse = await rosteringAPI.getDemographics({ studentIds });
    // eslint-disable-next-line no-empty
  } catch {}
  const demographicsByStudentId = demographicsResponse ? keyBy(demographicsResponse.flat(1), "sourcedId") : {};

  const teacherRoleNames = ["teacher", ...(userIdentifiers?.teacher || [])];
  const teacherEnrollmentsByClassId = groupBy(
    enrollments.filter(e => teacherRoleNames.includes(e.role.toLowerCase())),
    "classId"
  );

  const currentTeacherIds = [];
  const importData = [];

  const ncesBySchoolId = schools.reduce((a, c) => {
    if (!c.identifier || !c.name) {
      return a;
    }
    // eslint-disable-next-line no-param-reassign
    a[c.sourcedId] = { ncesId: c.identifier, name: c.name };
    return a;
  }, {});

  const gradeTranslationsByGrade = getGradeTranslationsByGrade(translations);

  classes.forEach(classDoc => {
    const { location, school, classCode, sourcedId: classId, title, course, grades } = classDoc;
    const { sourcedId: schoolId, name, parent: parentOrgEntity, identifier } = schools.find(
      s => s.sourcedId === school.sourcedId
    );
    const { sourcedId: parentSchoolId } = parentOrgEntity || {};
    const studentByClassId = groupBy(studentsWithClassId, "classId");
    const studentsInSection = get(studentByClassId, classId, []);
    const studentGradesList = mapExternalRosteringStudentGrades(
      getGradesFromOneRosterStudents(studentsInSection, gradeTranslationsByGrade)
    );
    const sectionGrade = getSectionGradeMajority(studentGradesList);

    const orderedTeacherEnrollmentsForClass = orderBy(
      uniqBy(teacherEnrollmentsByClassId[classId], "user.sourcedId") || [],
      ["primary"],
      ["desc"]
    );

    orderedTeacherEnrollmentsForClass.forEach(teacherEnrollment => {
      const teacher = teachersWithClassId.find(t => t.sourcedId === teacherEnrollment.user.sourcedId);
      if (!teacher) {
        return;
      }
      const { email, givenName: teacherFirstName, familyName: teacherLastName, sourcedId: teacherId } = teacher;
      currentTeacherIds.push(teacherId);
      studentsInSection.forEach(student => {
        const { familyName: studentLastName, givenName: studentFirstName, sourcedId: studentId } = student;
        const studentDemographics = demographicsByStudentId[studentId];
        const [studentGrade = sectionGrade] = getGradesFromOneRosterStudents([student], gradeTranslationsByGrade);
        const classSectionId = getClassSectionIdForOneRoster({ classId, school, course, classCode, grades });
        importData.push({
          DistrictID: orgid || parentSchoolId,
          DistrictName: orgName,
          SchoolID: (identifier || schoolId).toString().trim(),
          SchoolName: name,
          TeacherID: teacherId,
          TeacherLastName: teacherLastName,
          TeacherFirstName: teacherFirstName,
          TeacherEmail: email,
          ClassName: `${title}${location ? ` - ${location}` : ""} (${classSectionId})`,
          ClassSectionID: classSectionId,
          StudentLocalID: studentId,
          StudentStateID: studentId,
          StudentFirstName: studentFirstName.toString(),
          StudentBirthDate: studentDemographics?.birthDate || studentDemographics?.birthdate || "",
          StudentLastName: studentLastName.toString(),
          StudentGrade: studentGrade,
          SpringMathGrade: allowMultipleGradeLevels ? sectionGrade : studentGrade
        });
      });
    });
  });

  const newSchoolLabels = getNewSchoolLabels({ importData, existingSites });
  if (newSchoolLabels.length) {
    throw Error(
      `New schools can only be added during manual "Import Now".\nNew schools detected:\n${newSchoolLabels.join("\n")}`
    );
  }

  const allSelectedTeacherIds = uniq([...teacherIds, ...currentTeacherIds]);
  const newTeacherIds = difference(allSelectedTeacherIds, teacherIds);
  if (newTeacherIds.length) {
    await updateTeacherFilters(orgid, allSelectedTeacherIds);
  }

  return {
    ncesBySchoolId,
    importData
  };
}

function getNewSchoolLabels({ importData, existingSites }) {
  const { schoolNumbersToBeAdded } = getSchoolNumberComparison(importData, existingSites);

  return schoolNumbersToBeAdded
    .map(s => {
      const foundSite = importData.find(d => d.SchoolID === s);
      return foundSite ? `${foundSite.SchoolName} (${foundSite.SchoolID})` : null;
    })
    .filter(Boolean)
    .sort();
}

export async function getEdFiImportRowsDataFromComposite(orgid, existingSites = []) {
  const {
    filters,
    translations,
    name: orgName,
    shouldIgnoreEnrollmentStartDate,
    shouldIgnoreEnrollmentEndDate
  } = await getRosterSettings(orgid);
  const { allowMultipleGradeLevels = false } = await Organizations.findOneAsync(orgid);
  const descriptors = await fetchGradeLevelDescriptors(orgid);
  handleError({ resourceName: "descriptors", data: descriptors });
  const parsedDescriptors = descriptors.reduce((a, d) => {
    let { description } = d;
    const { codeValue } = d;
    if (["13", ">12"].includes(codeValue)) {
      description = "Grade 13";
    }
    // eslint-disable-next-line no-param-reassign
    a[codeValue] = description.toLowerCase();
    return a;
  }, {});
  const schools = await fetchSchoolsResource({ orgid, filters, useComposites: false });
  handleError({ resourceName: "schools", data: schools });
  const filteredSchools = schools.filter(s => filters.schools.includes(s.schoolId));
  const schoolDocIds = filteredSchools.map(s => s.id);
  const schoolIds = filteredSchools.map(s => s.schoolId);
  const staff = await fetchStaffsResource({ orgid, filters: { schoolIds: schoolDocIds }, useComposites: true });
  handleError({ resourceName: "staffs", data: staff });
  const staffDocIds = staff.map(s => s.id);
  const sectionsComposite = await fetchSectionsResource({
    orgid,
    filters: { teacherIds: staffDocIds },
    useComposites: true
  });
  handleError({ resourceName: "sections composite", data: sectionsComposite });
  const filteredSections = uniqBy(
    sectionsComposite.filter(s => filters.classes.includes(normalizeExternalRosteringId(s.id))),
    "id"
  );
  const sectionsData = await fetchSectionsResource({ orgid, filters: { schoolIds }, useComposites: false });
  const sectionsDataById = keyBy(sectionsData, "id");
  handleError({ resourceName: "sections resource", data: sectionsData });
  const schoolNameBySchoolId = filteredSchools.reduce((result, school) => {
    // eslint-disable-next-line no-param-reassign
    result[school.schoolId] = {
      nameOfInstitution: school.nameOfInstitution,
      localEducationAgencyId: get(school, "localEducationAgencyReference.localEducationAgencyId")
    };
    return result;
  }, {});

  // FIXME(fmazur) - schoolIds -> sectionIds
  const students = await fetchStudentsResource({
    orgid,
    schoolIds: filteredSections.map(c => c.id),
    useComposites: true
  });

  const gradeTranslationsByGrade = getGradeTranslationsByGrade(translations);
  const gradeByGradeTranslation = getGradeByGradeTranslation(gradeTranslationsByGrade);

  const currentTeacherIds = [];
  const importData = [];
  filteredSections.forEach(section => {
    const { courseOfferingReference, classPeriods, sectionName, locationReference, sectionIdentifier } =
      sectionsDataById[normalizeExternalRosteringId(section.id)] || {};
    const { classroomCode, className } = getExternalRosteringClassDetails({
      locationReference,
      classPeriods,
      courseOfferingReference,
      sectionName,
      sectionIdentifier
    });
    const staffAssociationsInSection = section.staff || [];
    const dateNow = Date.now();
    const studentAssociationsByStudentUniqueId = keyBy(
      section.students.filter(
        s =>
          (!s.enrollmentBeginDate || shouldIgnoreEnrollmentStartDate || new Date(s.enrollmentBeginDate) <= dateNow) &&
          (!s.enrollmentEndDate || shouldIgnoreEnrollmentEndDate || new Date(s.enrollmentEndDate) >= dateNow)
      ),
      "studentUniqueId"
    );
    const studentsAssociationsInSection = Object.values(studentAssociationsByStudentUniqueId);
    const staffByStaffUniqueId = keyBy(
      staff.map(s => ({ ...s, id: normalizeExternalRosteringId(s.id) })),
      "staffUniqueId"
    );
    const studentDocIds = studentsAssociationsInSection.map(s => normalizeExternalRosteringId(s.id));
    const studentsInSection = students.filter(s => studentDocIds.includes(normalizeExternalRosteringId(s.id)));
    const studentsByUniqueId = keyBy(studentsInSection, "studentUniqueId");
    const schoolName = get(schoolNameBySchoolId[courseOfferingReference.schoolId], "nameOfInstitution", "N/A");
    const studentsInSectionWithGrades = studentsInSection.map(s => {
      const { entryGradeLevelDescriptor = "" } =
        (s.studentSchoolEnrollments || s.schools).find(se => se.schoolId === locationReference.schoolId) ||
        (s.studentSchoolEnrollments || s.schools)[0] ||
        {};
      return {
        studentUniqueId: s.studentUniqueId,
        studentGrade: getGradeFromDescriptor(entryGradeLevelDescriptor, parsedDescriptors, gradeByGradeTranslation)
      };
    });
    const sectionGrade = getSectionGradeMajority(
      studentsInSectionWithGrades.map(s => getGradeLabel(s.studentGrade) || s.studentGrade)
    );
    const studentsInSectionWithGradeByUniqueId = keyBy(studentsInSectionWithGrades, "studentUniqueId");
    staffAssociationsInSection.forEach(({ staffUniqueId = "" }) => {
      const { firstName, lastSurname, electronicMails = [] } = get(staffByStaffUniqueId, staffUniqueId, {});
      const teacherEmail = get(electronicMails[0], "electronicMailAddress", "");
      currentTeacherIds.push(staffUniqueId);
      importData.push(
        ...studentsAssociationsInSection.map(({ studentUniqueId }) => {
          const student = studentsByUniqueId[studentUniqueId] || {};
          const studentGrade = studentsInSectionWithGradeByUniqueId[studentUniqueId]?.studentGrade;
          return {
            DistrictID: get(
              schoolNameBySchoolId[courseOfferingReference.schoolId],
              "localEducationAgencyId",
              "N/A"
            ).toString(),
            DistrictName: orgName, // SpringMath side organization name
            SchoolID: courseOfferingReference.schoolId.toString(),
            SchoolName: schoolName,
            TeacherID: staffUniqueId,
            TeacherLastName: lastSurname,
            TeacherFirstName: firstName,
            TeacherEmail: teacherEmail,
            ClassName: className,
            ClassSectionID: classroomCode,
            StudentLocalID: studentUniqueId,
            StudentStateID: studentUniqueId,
            StudentFirstName: student.firstName,
            StudentBirthDate: student.birthDate || "",
            StudentLastName: student.lastSurname,
            StudentGrade: studentGrade,
            SpringMathGrade: allowMultipleGradeLevels ? sectionGrade : studentGrade
          };
        })
      );
    });
  });

  const newSchoolLabels = getNewSchoolLabels({ importData, existingSites });
  if (newSchoolLabels.length) {
    throw Error(
      `New schools can only be added during manual "Import Now".\nNew schools detected:\n${newSchoolLabels.join("\n")}`
    );
  }

  const allSelectedTeacherIds = uniq([...filters.teachers, ...currentTeacherIds]);
  const newTeacherIds = difference(allSelectedTeacherIds, filters.teachers);
  if (newTeacherIds.length) {
    await updateTeacherFilters(orgid, allSelectedTeacherIds);
  }

  return importData;
}

// eslint-disable-next-line no-unused-vars
async function getEdFiImportRowsData(orgid) {
  const { filters, name: orgName } = await getRosterSettings(orgid);
  const schoolsPromise = fetchSchoolsResource({ orgid, filters, useComposites: false });
  const staffsPromise = fetchStaffsResource({ orgid, filters, useComposites: false });
  const sectionsPromise = fetchSectionsResource({ orgid, filters, useComposites: false });
  const [schools, staffs, sections] = await Promise.all([schoolsPromise, staffsPromise, sectionsPromise]);
  handleError({ resourceName: "schools", data: schools });
  handleError({ resourceName: "staffs", data: staffs });
  handleError({ resourceName: "sections", data: sections });
  const staffSectionAssociationsPromise = fetchStaffSectionAssociationsResource({
    orgid,
    sections,
    useComposites: false
  });
  const studentSectionAssociationsPromise = fetchStudentSectionAssociationsResource({
    orgid,
    sections,
    useComposites: false
  });
  const [staffSectionAssociations, studentAssociations] = await Promise.all([
    staffSectionAssociationsPromise,
    studentSectionAssociationsPromise
  ]);
  const studentAssociationsByIdAndStudentUniqueId = keyBy(studentAssociations, studentAssociation => {
    const sectionIdentifier = studentAssociation?.sectionReference?.sectionIdentifier || "";
    const studentUniqueId = studentAssociation?.studentReference?.studentUniqueId || "";
    return `${sectionIdentifier}_${studentUniqueId}`;
  });

  const studentSectionAssociations = Object.values(studentAssociationsByIdAndStudentUniqueId);
  handleError({ resourceName: "staffSectionAssociations", data: staffSectionAssociations });
  handleError({ resourceName: "studentSectionAssociations", data: studentSectionAssociations });
  const students = await fetchStudentsResource({ orgid, studentSectionAssociations, useComposites: false });
  handleError({ resourceName: "students", data: students });

  const staffAssociationBySectionIdentifier = keyBy(staffSectionAssociations, "sectionReference.sectionIdentifier");
  const schoolNameBySchoolId = schools.reduce((result, school) => {
    // eslint-disable-next-line no-param-reassign
    result[school.schoolId] = {
      nameOfInstitution: school.nameOfInstitution,
      localEducationAgencyId: get(school, "localEducationAgencyReference.localEducationAgencyId")
    };
    return result;
  }, {});
  const staffByStaffUniqueId = keyBy(staffs, "staffUniqueId");
  const studentAssociationsBySectionIdentifier = groupBy(
    studentSectionAssociations,
    "sectionReference.sectionIdentifier"
  );
  const studentByUniqueId = keyBy(students, "studentUniqueId");
  const studentDocumentsBySectionIdentifier = Object.entries(studentAssociationsBySectionIdentifier).reduce(
    (acc, [sectionId, kidAssociations]) => {
      if (!acc[sectionId]) {
        acc[sectionId] = [];
      }
      kidAssociations.forEach(kid => {
        const student = studentByUniqueId[kid.studentReference.studentUniqueId];
        if (student) {
          acc[sectionId].push(student);
        }
      });
      return acc;
    },
    {}
  );

  const importData = [];
  sections.forEach(section => {
    const {
      courseOfferingReference,
      sectionIdentifier,
      classPeriods,
      sectionName,
      locationReference,
      offeredGradeLevels
    } = section;
    const { classroomCode, className } = getExternalRosteringClassDetails({
      locationReference,
      classPeriods,
      courseOfferingReference,
      sectionName,
      sectionIdentifier
    });
    const { staffUniqueId = "" } = get(staffAssociationBySectionIdentifier[sectionIdentifier], "staffReference", {});
    const { firstName, lastSurname, electronicMails } = get(staffByStaffUniqueId, staffUniqueId, {});
    const parsedSchool = schoolNameBySchoolId[courseOfferingReference.schoolId];
    const schoolName = get(parsedSchool, "nameOfInstitution", "N/A");
    const tempEmailObject = Array.isArray(electronicMails) ? electronicMails[0] : electronicMails;
    const teacherEmail = get(tempEmailObject, "electronicMailAddress", "");
    const grade = get(offeredGradeLevels, 0, "");

    importData.push(
      ...studentDocumentsBySectionIdentifier[sectionIdentifier].map(sd => ({
        DistrictID: get(parsedSchool, "localEducationAgencyId", "N/A").toString(),
        DistrictName: orgName, // SpringMath side organization name
        SchoolID: courseOfferingReference.schoolId.toString(),
        SchoolName: schoolName,
        TeacherID: staffUniqueId,
        TeacherLastName: lastSurname,
        TeacherFirstName: firstName,
        TeacherEmail: teacherEmail,
        ClassName: className,
        ClassSectionID: classroomCode,
        StudentLocalID: sd.studentUniqueId,
        StudentStateID: sd.studentUniqueId,
        StudentFirstName: sd.firstName,
        StudentBirthDate: sd.birthDate || "",
        StudentLastName: sd.lastSurname,
        SpringMathGrade: grade
      }))
    );
  });

  return importData;
}

function handleRosterImportValidationErrors({
  orgid,
  validationErrors,
  rosterImportId,
  itemCount,
  shouldUpdateStatus = true
}) {
  failRosterImport({
    error: {
      reason: validationErrors.join("\n\n")
    },
    rosterImportId,
    hasImportError: true,
    shouldUpdateStatus,
    shouldThrow: false,
    itemCount
  });
  sendEmailForFailedRosterImport(orgid, rosterImportId);
}

async function doesImportNeedManualIntervention({ orgid, importData, userId }) {
  const existingSites = await Sites.find(
    { orgid },
    { fields: { "stateInformation.schoolNumber": 1, schoolYear: 1, lastModified: 1, name: 1 } }
  ).fetchAsync();
  const { matchedExistingSchoolNumbers, importDataSchoolNumbersByExistingSchoolNumbers } = getSchoolNumberComparison(
    importData,
    existingSites
  );

  const areSitesUnmatched = !areSitesMatching(
    matchedExistingSchoolNumbers,
    importData.map(i => i.SchoolID)
  );

  const siteIds = existingSites.map(s => s._id);
  const user = (await Users.findOneAsync({ _id: userId })) || {};
  const schoolYear = await getCurrentSchoolYear(user, orgid);
  const existingStudentGroups = await StudentGroups.find(
    { siteId: { $in: siteIds }, schoolYear },
    { fields: { sectionId: 1, siteId: 1, isActive: 1 } }
  ).fetchAsync();
  const areAnyStudentGroupsUnmatched = areAnyStudentGroupsUnmatchedInSites({
    importData,
    existingSites,
    studentGroups: existingStudentGroups,
    importDataSchoolNumbersByExistingSchoolNumbers
  });

  return areSitesUnmatched || areAnyStudentGroupsUnmatched;
}

export async function validateAndImportRowData(orgid, rosterImportId) {
  const rosteringType = await fetchRosteringType(orgid);
  let importData = [];
  let ncesBySchoolId = {};
  const existingSites = await Sites.find(
    { orgid },
    { fields: { "stateInformation.schoolNumber": 1, "lastModified.on": 1 } }
  ).fetchAsync();
  if (rosteringType === "rosterEdFi") {
    importData = await getEdFiImportRowsDataFromComposite(orgid, existingSites);
    // importData = await getEdFiImportRowsData(orgid);
  }
  if (rosteringType === "rosterOR") {
    ({ importData, ncesBySchoolId } = await getOneRosterImportRowsData(orgid, rosteringType, existingSites));
  }

  const source = getRosteringTypeLabel(rosteringType);
  const customUserId = `CRON_${source}`;

  const lastCompletedRosterImportItemCount =
    (await RosterImports.findOneAsync(
      { orgid, _id: { $ne: rosterImportId }, status: "completed" },
      {
        sort: { "started.on": -1 },
        fields: { itemCount: 1 }
      }
    )?.itemCount) || 0;

  const dataChangeThreshold =
    (await Settings.findOneAsync({}, { fields: { "defaults.rosteringThreshold": 1 } })?.defaults?.rosteringThreshold) ||
    0.5;
  if (importData.length < lastCompletedRosterImportItemCount * (1 - dataChangeThreshold)) {
    handleRosterImportValidationErrors({
      orgid,
      validationErrors: [
        "The sync schedule for SpringMath detected a large change to the data and has been cancelled. Please manually run an import if this change is intended. If this change is not intended, there may be an issue."
      ],
      rosterImportId,
      shouldUpdateStatus: true,
      itemCount: importData.length
    });
    return null;
  }

  return validateAndSetSubtotals({
    data: importData,
    isCSV: false,
    shouldDisplayLoading: false,
    callback: async ({ errors: validationErrors, failedRows }) => {
      const hasValidationErrors = validationErrors.length > 0;
      const shouldShowStudentsInMultipleClassesInfo =
        hasValidationErrors &&
        validationErrors.some(validationError =>
          validationError.includes("More than one classSectionID is present for studentLocalID")
        );
      if (shouldShowStudentsInMultipleClassesInfo && !validationErrors.includes(studentsInMultipleClassesInfo)) {
        validationErrors.unshift(studentsInMultipleClassesInfo);
      }
      const itemCount = importData.length;
      const data = importData.filter((datum, index) => !failedRows.includes(index));
      if (data.length || (!data.length && !failedRows.length)) {
        if (Object.keys(ncesBySchoolId).length) {
          convertSchoolIdsToNCES(ncesBySchoolId, orgid);
        }

        if (await doesImportNeedManualIntervention({ orgid, importData, userId: customUserId })) {
          handleRosterImportValidationErrors({
            orgid,
            validationErrors: [
              'There are import data conflicts that need to be resolved. It requires manual intervention by using the "Import Now" rostering feature.'
            ],
            rosterImportId,
            shouldUpdateStatus: true
          });
          return;
        }

        try {
          insertRoster({ data, source, itemCount }, customUserId, orgid, rosterImportId);
          if (hasValidationErrors) {
            handleRosterImportValidationErrors({ orgid, validationErrors, rosterImportId, shouldUpdateStatus: false });
          }
        } catch (e) {
          sendEmailForFailedRosterImport(orgid, rosterImportId);
        }
      } else {
        const lastModified = await getTimestampInfo(getMeteorUserId(), orgid, "validateAndImportRowData");
        await RosterImports.updateAsync(rosterImportId, { $set: { itemCount, lastModified } });
        handleRosterImportValidationErrors({ orgid, validationErrors, rosterImportId });
      }
    }
  });
}
