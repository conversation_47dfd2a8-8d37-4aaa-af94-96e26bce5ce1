// Skill Progress Print Styles - For both Individual Student and Class Skill Progress
// This only affects the print output from skill progress modals (both student and class views)

@media print {
  .skill-progress-print-page {
    // Scale down to fit more content on portrait
    zoom: 0.75;
    width: 100%;
    
    // Fix blank first page issue
    break-before: avoid !important;
    break-inside: avoid !important;
    
    // Compact header
    .page-header {
      margin-bottom: 10px !important;
      break-after: avoid !important;
      break-inside: avoid !important;
      h1 {
        font-size: 16px !important;
        margin: 0 0 5px 0 !important;
      }
      p {
        font-size: 11px !important;
        margin: 0 !important;
      }
    }
    
    // Ensure main content starts immediately after header
    .main-content {
      break-before: avoid !important;
      margin-top: 0 !important;
      padding-top: 0 !important;
    }
    
    // Optimize table for landscape printing
    .table {
      width: 100% !important;
      max-width: none !important;
      
      &.overflow-auto {
        overflow: visible !important;
      }
    }
    
    // Adjust progress table specifically
    .progress-table {
      font-size: 8px !important;
      
      th, td {
        padding: 1px 3px !important;
        font-size: 8px !important;
        line-height: 1.2 !important;
      }
      
      // Handle sideways headers
      .sideways {
        max-height: 120px !important;
        font-size: 8px !important;
      }
    }
    
    // Optimize dots for print
    .dot, .dot-small {
      width: 10px !important;
      height: 10px !important;
      display: inline-block !important;
      margin: 0 1px !important;
    }
    
    // Compact legend
    .d-flex.flex-row.gap-3 {
      gap: 0.5rem !important;
      font-size: 10px !important;
      margin-bottom: 5px !important;
    }
    
    // Ensure no page breaks within table
    table {
      page-break-inside: avoid;
    }
    
    tbody tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }
    
    // Force single column for wide tables
    .text-nowrap {
      white-space: normal !important;
      max-width: 80px !important;
      font-size: 8px !important;
    }
    
    // Optimize percentage displays
    .vertical-align-middle div {
      font-size: 7px !important;
      line-height: 1 !important;
    }
    
    // Ensure colors print correctly
    .text-black {
      color: #000 !important;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }
  }
}