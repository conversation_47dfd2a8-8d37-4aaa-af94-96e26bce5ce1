#!/bin/bash

arch=$(uname -m)
# Use OVHCloud registry
REGISTRY="${OVHCLOUD_REGISTRY_URL:-605ipw29.c1.va1.container-registry.ovh.us}"
PROJECT="${OVHCLOUD_REGISTRY_PROJECT_NAME:-springmath}"

image="${REGISTRY}/${PROJECT}/spring-math-test-ci:8.1"

if [ "$arch" = "arm64" ] || [ "$1" = "arm" ]; then
    image+="-arm"
fi

echo "Starting MongoDB container from: $image"
docker run --rm -it -p 27017:27017 $image /bin/bash startMongo.sh
