/* eslint-disable cypress/unsafe-to-chain-command */
import {
  clickVisibleText,
  continueScreeningFor,
  startScreeningFor,
  waitForNthScoreToBeSaved
} from "../support/common/utils";
import { clearAllScores } from "../support/enterScoreHelper";
import { goToStudentListFor } from "../support/common/navigating";
import { TEST_GROUPS } from "../support/common/constants";

const failingScore = "0";
const screeningResultsUrlRegExp = new RegExp("student-groups\\/([^\\/]*)\\/screening\\/results\\/([^\\/]*)");

/**
 * Groups used: grade1group2, grade2group2, gradeKgroup3, gradeKgroup1
 * Modifies: -
 * Can't be rerun without test database restart
 * Requires groups without screening intervention
 */
describe("Screening Assessment:", () => {
  describe("Teacher", () => {
    beforeEach(() => {
      cy.loginAs({ role: "teacher", queryString: "?siteId=test_elementary_site_id" });
    });

    it("sees unusual high score warning for scores over the limit", () => {
      startScreeningFor(TEST_GROUPS.grade2group2.name);
      inputScreeningScores(false);
      cy.get("#score_input_0")
        .clear()
        .should("have.value", "")
        .type("999");
      cy.findByText("Unusual High Score").should("be.visible");
      cy.findByText("Fix Unusual High Scores", { exact: false }).should("be.visible");
      clearAllScores();
    });

    it("sees screening summary when results are passing", () => {
      continueScreeningFor(TEST_GROUPS.grade2group2.name);
      inputScreeningScores(true);
      saveScreeningScoresAndGoToResults();

      cy.url().should("match", screeningResultsUrlRegExp);
      cy.findByText("Screening Results", { exact: false }).should("be.visible");
      cy.findByText("100% of students are meeting all requirements.").should("be.visible");
    });

    it("sees started classwide intervention when majority of the group scored below target", () => {
      startScreeningFor(TEST_GROUPS.grade1group2.name);
      inputScreeningScores(false);
      saveScreeningScoresAndGoToResults();

      cy.contains("0% of your class reached the target on all of the screening assessments.").should("be.visible");
      cy.findByTestId("classwideInterventionTab").should("be.visible");
      cy.findByText("To the Current Intervention")
        .scrollIntoView()
        .should("be.visible");
    });

    it("sees individual intervention eligibility for individual students that scored below classwide median", () => {
      // TODO(fmazur) - check whether moveStudents spec impacts this step
      startScreeningFor(TEST_GROUPS.gradeKgroup3.name);
      inputScreeningScores(true);
      cy.findAllByTestId("scoreInput").each((input, index) => {
        if (index < 6) {
          cy.wrap(input).clear();
          cy.wrap(input).type(failingScore);
        }
      });
      saveScreeningScoresAndGoToResults();

      cy.get("#individual-screening-summary").scrollIntoView();
      cy.findByText("who could benefit from intervention", { exact: false }).should("be.visible");
      cy.findAllByTestId("scheduleInterventionCheckbox").should("have.length", 2);
      cy.findAllByTestId("scheduleInterventionCheckbox").each(checkbox => cy.wrap(checkbox).click());
      cy.findByText("Begin Individual Interventions")
        .should("not.be.disabled")
        .click();
      // cy.findByTestId(`close-and-assign-individual-intervention-button`)
      //   .should("be.visible")
      //   .click();
      cy.findByText("Individual interventions scheduled successfully").should("be.visible");
      cy.findByTestId("individualInterventionTab").should("be.visible");
    });

    it("sees individual intervention eligibility when group has a single student that failed", () => {
      startScreeningFor(TEST_GROUPS.gradeKgroup1.name);
      inputScreeningScores(false);
      saveScreeningScoresAndGoToResults();

      cy.findByTestId("interventionCard_Cannon, Fred")
        .scrollIntoView()
        .should("be.visible");
      cy.findByText("Individual intervention takes 15-20 minutes a day.").should("be.visible");
    });
  });

  describe("Data Admin clears screening", () => {
    it("and sees the cleared screening", () => {
      cy.loginAs({ role: "dataAdmin" });
      cy.url().should("contain", "data-admin");
      goToStudentListFor(TEST_GROUPS.grade1group2.name);

      clickVisibleText("Manage Scores");
      cy.findByText("Screenings").should("be.visible");
      cy.findByText("No individual intervention scores for this group found").should("be.visible");
      cy.findByText("No classwide intervention scores for this group found").should("be.visible");
      clickVisibleText("Clear Scores");
      clickVisibleText("Yes, clear screening scores");
      cy.findByText("Screening scores have been cleared successfully").should("be.visible");
      cy.findByText("In progress").should("be.visible");
    });
  });
});

function saveScreeningScoresAndGoToResults() {
  clickVisibleText("View Results");
  clickVisibleText("Continue to results");
}

function inputScreeningScores(isPassing) {
  cy.findAllByTestId("scoreInput").each(input =>
    cy.wrap(input).then(([$input]) => {
      cy.wrap(input).type(isPassing ? $input.dataset.assessmentScoreLimit : 0, { delay: 10 });
    })
  );
  if (isPassing) {
    waitForNthScoreToBeSaved();
  }
}
