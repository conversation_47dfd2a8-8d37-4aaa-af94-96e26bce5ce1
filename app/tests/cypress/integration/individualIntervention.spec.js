/* eslint-disable cypress/unsafe-to-chain-command */
import {
  enterAndPreviewGoalSkillScore,
  enterAndSaveInterventionAndGoalSkillScores,
  inputAndSaveGoalScore,
  inputAndSaveInterventionScore
} from "../support/enterScoreHelper";
import {
  clickElementByTextWithinSideNav,
  clickDashboardTab,
  goToGroupIndividualIntervention
} from "../support/common/navigating";
import {
  clickVisibleTestId,
  expandInterventionForStudent,
  waitForDetachedDom,
  waitForNthScoreToBeSaved
} from "../support/common/utils";
import { TEST_GROUPS } from "../support/common/constants";

const verifyNumberOfChartPoints = numberOfPoints => {
  cy.get(".highcharts-point").should($listOfElements => {
    expect($listOfElements).to.have.length(numberOfPoints);
  });
};

const manuallyScheduleInterventionForStudent = studentId => {
  cy.findByTestId(`student-dropdown-${studentId}`)
    .scrollIntoView()
    .should("be.visible")
    .click();
  clickVisibleTestId(`schedule_intervention_${studentId}`);
  waitForDetachedDom();
};

const manuallyEndInterventionForStudent = studentId => {
  clickVisibleTestId(`student-dropdown-${studentId}`);
  clickVisibleTestId(`end_intervention_${studentId}`);
  waitForDetachedDom();
};

const CURRENT_SCHOOL_YEAR = Cypress.env("CURRENT_SCHOOL_YEAR");

const verifyNumberOfGoalAndInterventionSkillPoints = (numberOfGoalSkillPoints, numberOfInterventionSkillPoints) => {
  // eslint-disable-next-line cypress/no-unnecessary-waiting
  cy.wait(1000); // NOTE(fmazur) - make sure elements finish transitioning in Highchart/Highstock graph
  cy.findByTestId("goal-skill-graph")
    .scrollIntoView()
    .should("be.visible")
    .within(() => {
      verifyNumberOfChartPoints(numberOfGoalSkillPoints);
    });

  cy.findByTestId("intervention-skill-graph")
    .scrollIntoView()
    .should("be.visible")
    .within(() => {
      verifyNumberOfChartPoints(numberOfInterventionSkillPoints);
    });
};

const verifyTwoSkillButtonLabelsInSkillContainer = (expectedSkillText, expectedGoalText) => {
  cy.get(".skill-score-entry")
    .first()
    .within(() => {
      cy.findByText(expectedSkillText).should("be.visible");
      cy.findByText(expectedGoalText).should("be.visible");
    });
};

const verifyInterventionSkillHasDetailText = (expectedText, studentId) => {
  waitForDetachedDom();
  cy.get(`[data-student-id=${studentId}]`)
    .first()
    .scrollIntoView()
    .should("be.visible")
    .within(() => {
      cy.get(".skill-details")
        .first()
        .within(() => {
          cy.findByText(expectedText).should("be.visible");
        });
      cy.get(".skill-score-entry")
        .first()
        .within(() => {
          cy.findByText("Enter Scores to Continue").should("be.visible");
        });
    });
};

const enterAndPreviewInterventionAndGoalSkillScores = (skillScore, goalSkillScore) => {
  cy.get(".skill-score-entry")
    .first()
    .scrollIntoView()
    .within(() => {
      cy.findByTestId("enterSkillScore").then(([$input]) => {
        cy.findByTestId("enterSkillScore").type(
          parseInt(skillScore) > parseInt($input.dataset.assessmentScoreLimit)
            ? $input.dataset.assessmentScoreLimit
            : skillScore
        );
      });
      cy.findByTestId("enterGoalScore").then(([$input]) => {
        cy.findByTestId("enterGoalScore").type(
          parseInt(goalSkillScore) > parseInt($input.dataset.assessmentScoreLimit)
            ? $input.dataset.assessmentScoreLimit
            : goalSkillScore
        );
      });
      cy.findByTestId("saveScoreBtn")
        .scrollIntoView()
        .should("be.visible")
        .should("not.be.disabled");
    });
};

const openManageInterventionModal = studentId => {
  waitForDetachedDom();
  manuallyScheduleInterventionForStudent(studentId);
  cy.findByTestId("manage-individual-intervention-modal").should("be.visible");
};

function chooseManageInterventionOption(actionName) {
  const actionRadioValue = `[value=${actionName}]`;
  cy.get(actionRadioValue)
    .click()
    .should("be.checked");
  cy.findByText("Submit").click();
}

const continueInterventionToTheNextSkillTree = studentId => {
  waitForDetachedDom(500);
  clickVisibleTestId(`continue_individual_button_${studentId}`);
  waitForDetachedDom(500);
};

/**
 * Groups used: grade7group1, grade3group2
 * Modifies: Screening
 * Can't be rerun without test database restart
 * Requires Student group without screening
 */
describe("Individual Intervention Assessment:", () => {
  const dylanMackStudentId = `D2fG2eJjNiMDe3QCh${CURRENT_SCHOOL_YEAR}`;
  const brucePatrickStudentId = `zv7dJbCk8cz9tNdiz${CURRENT_SCHOOL_YEAR}`;
  const aliceAustinStudentId = `DAE5S4Au7tzyvWsz9${CURRENT_SCHOOL_YEAR}`;
  const dylanMackIIContainerSelector = `[data-student-id=${dylanMackStudentId}]`;
  const brucePatrickIIContainerSelector = `[data-student-id=${brucePatrickStudentId}]`;
  const aliceAustinIIContainerSelector = `[data-student-id=${aliceAustinStudentId}]`;
  describe("Follow-up (Drill-down) Assessments:", () => {
    describe("Teacher", () => {
      beforeEach(() => {
        cy.loginAs({ role: "teacher", queryString: "?siteId=test_elementary_site_id" });
        goToGroupIndividualIntervention(TEST_GROUPS.grade7group1.name);
      });
      it("dismisses starting intervention message", () => {
        cy.get(dylanMackIIContainerSelector)
          .first()
          .within(() => {
            cy.findByText("We need to administer a quick assessment before starting interventions with Dylan.").should(
              "be.visible"
            );
            clickVisibleTestId("dismissInterventionMessage");
          });
        cy.get(dylanMackIIContainerSelector)
          .first()
          .within(() => {
            cy.get(".conInterventionMessageNotice").should("not.exist");
          });
      });
      it("sees currently scheduled individual interventions in student page", () => {
        clickDashboardTab(TEST_GROUPS.grade7group1.name, "studentsTab");
        cy.findByTestId("currentIntAssessmentRow_0")
          .findByText("Mixed Operations")
          .should("be.visible");
        cy.findByTestId("currentIntAssessmentRow_1")
          .findByText("Order of Operations")
          .should("be.visible");
      });
      it("sees Follow-Up assessment", () => {
        cy.get(dylanMackIIContainerSelector)
          .first()
          .within(() => {
            cy.findByText("Dylan Mack").should("be.visible");
            cy.findByText("is currently completing follow ups to their screening assessments.").should("be.visible");
            cy.findByText("Mixed Operations").should("be.visible");
            cy.findByText("Generate Assessment").should("be.visible");
            cy.findByPlaceholderText("Enter Score").should("be.visible");
            cy.findByText("Enter Scores to Continue").should("be.visible");
          });
      });
      it("sees score input validation", () => {
        cy.get(dylanMackIIContainerSelector)
          .first()
          .within(() => {
            cy.findByTestId("enterSkillScore").type("999");
            cy.findByText("Unusual High Score").should("be.visible");
            cy.findByText("Fix Unusual High Score", { exact: false }).should("be.visible");
            cy.findByTestId("enterSkillScore").clear();

            const scoreValue = "1";
            cy.findByTestId("enterSkillScore").type(scoreValue);
            cy.findByText("Save Results").should("be.visible");

            cy.findByTestId("enterSkillScore").clear();
            cy.findByText("Enter Scores to Continue").should("be.visible");

            const faultyInput = "badScore";
            cy.findByTestId("enterSkillScore").type(faultyInput);
            cy.findByText("Integers Only").should("be.visible");
            cy.findByTestId("enterSkillScore").clear();
            waitForNthScoreToBeSaved({ testId: "enterSkillScore" });
          });
      });
      it("sees Individual Intervention after scoring above mastery target value in Follow-Up assessment", () => {
        const aboveValue = "20";
        cy.get(dylanMackIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveInterventionScore(aboveValue);
          });
        cy.contains(/Order of Operations.*Acquisition/).should("be.visible");
        expandInterventionForStudent("Dylan Mack");
        cy.get(dylanMackIIContainerSelector)
          .first()
          .within(() => {
            cy.contains("We have found an appropriate intervention for Dylan to practice.").should("be.visible");
            cy.findByText("Dylan Mack").should("be.visible");
            cy.findByText("is currently practicing the skill").should("be.visible");
            cy.get(".skill-details")
              .first()
              .within(() => {
                cy.findByText("Order of Operations").should("be.visible");
              });
            cy.get(".skill-score-entry")
              .first()
              .within(() => {
                cy.findByPlaceholderText("Enter Score").should("be.visible");
                cy.findByText("Order of Operations").should("be.visible");
              });
            // GRAPH
            cy.get(".skill-graph")
              .first()
              .within(() => {
                cy.findByText("Goal Skill:").should("be.visible");
                cy.findByText("Order of Operations").should("be.visible");
                cy.findByText("Mastery Target (8)").should("be.visible");
                cy.findByText("Instructional Target (4)")
                  .should("be.visible")
                  .then(() => {
                    verifyNumberOfChartPoints(0);
                  });
              });
            // PRINTOUT OPTIONS
            cy.findByText("Select Activity")
              .scrollIntoView()
              .should("be.visible")
              .click();
            cy.findByText("Cover, Copy, Compare").isPseudoVisible();
            cy.findByText("Guided Practice").isPseudoVisible();
          });
      });
      it("sees different outcomes when previewing different scores", () => {
        cy.get(dylanMackIIContainerSelector)
          .first()
          .within(() => {
            let scoreValue = "0";
            enterAndPreviewGoalSkillScore(scoreValue);
            cy.contains(
              `Goal Skill score of ${scoreValue}! Great job. We will continue practicing this Intervention Skill.`
            ).should("be.visible");
            cy.contains("Save the scores to continue the intervention for Dylan Mack.")
              .should("be.visible")
              .then(() => {
                verifyNumberOfChartPoints(1);
              });
            cy.findByTestId("enterGoalScore").clear();
            waitForNthScoreToBeSaved({ testId: "enterGoalScore" });
            verifyNumberOfChartPoints(0);

            scoreValue = "3";
            enterAndPreviewGoalSkillScore(scoreValue);
            cy.contains(
              `Goal Skill score of ${scoreValue}! Great job. We will continue practicing this Intervention Skill.`
            ).should("be.visible");
            cy.contains("also improved on the Goal Skill!").should("be.visible");

            scoreValue = "5";
            enterAndPreviewGoalSkillScore(scoreValue);
            cy.contains(
              `Goal Skill score of ${scoreValue}! Excellent work. There will be new practice materials next time.`
            ).should("be.visible");

            scoreValue = "11";
            enterAndPreviewGoalSkillScore(scoreValue);
            cy.contains(`Goal Skill score of ${scoreValue}! Excellent work.`).should("be.visible");
            cy.contains("Save the scores to determine if Dylan Mack should move on to a new goal skill.").should(
              "be.visible"
            );
            cy.findByTestId("enterGoalScore").clear();
            waitForNthScoreToBeSaved({ testId: "enterGoalScore" });
          });
      });
      it("sees completed goal skill after scoring above mastery target", () => {
        const overMasteryTargetScore = "11";
        enterAndPreviewGoalSkillScore(overMasteryTargetScore);
        cy.contains(`Goal Skill score of ${overMasteryTargetScore}! Excellent work.`).should("be.visible");
        cy.findByText("Save Results").click();
        continueInterventionToTheNextSkillTree(dylanMackStudentId);
        cy.findAllByText("Mixed Inverse Operations - Add, Subtract, Multiply, Divide").should("be.visible");
      });
    });
  });

  describe("Individual Intervention:", () => {
    describe("Teacher", () => {
      beforeEach(() => {
        cy.loginAs({ role: "teacher", queryString: "?siteId=test_elementary_site_id" });
        goToGroupIndividualIntervention(TEST_GROUPS.grade3group2.name);
      });
      it("sees intervention assigned when reached the end of the decision tree by scoring below targets", () => {
        cy.findAllByText("Multiplication 0-5")
          .should("be.visible")
          .should("have.length", 2);
        cy.findAllByText("Multiplication 0-9")
          .should("be.visible")
          .should("have.length.at.least", 2);

        const failingScore = "0";
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveInterventionScore(failingScore);
          }); // Multiplication 0-5 drill down - case where skill is expanded by default due to other student
        cy.findAllByText("Multiplication 0-5")
          .should("be.visible")
          .should("have.length", 4);
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveInterventionScore(failingScore);
          });
        cy.findByText("Sums to 12").should("be.visible");
        expandInterventionForStudent("Bruce Patrick");
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveInterventionScore(failingScore);
          });
        cy.findByText("Sums to 6").should("be.visible");
        expandInterventionForStudent("Bruce Patrick");
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveInterventionScore(failingScore);
          });
        cy.contains(/Sums to 6.*Acquisition/).should("be.visible");
        expandInterventionForStudent("Bruce Patrick");
        verifyNumberOfGoalAndInterventionSkillPoints(0, 1);
      });
      it("sees the scores can only be saved when all values are present", () => {
        const testScore = "5";
        cy.get(".skill-score-entry")
          .first()
          .scrollIntoView()
          .within(() => {
            cy.findByText("Enter Scores to Continue").should("be.visible");

            cy.findByTestId("enterGoalScore").type("999");
            cy.findByText("Unusual High Score").should("be.visible");
            cy.findByText("Fix Unusual High Score", { exact: false }).should("be.visible");
            cy.findByTestId("enterGoalScore").clear();

            cy.findByTestId("enterSkillScore").type(testScore);
            cy.findByText("Enter Scores to Continue").should("be.visible");

            cy.findByTestId("enterGoalScore").type(testScore);
            cy.findByTestId("saveScoreBtn").should("be.visible");

            cy.findByTestId("enterSkillScore").clear();
            waitForNthScoreToBeSaved({ testId: "enterSkillScore" });
            cy.findByText("Enter Scores to Continue").should("be.visible");

            cy.findByTestId("enterGoalScore").clear();
            waitForNthScoreToBeSaved({ testId: "enterGoalScore" });
            cy.findByText("Enter Scores to Continue").should("be.visible");
          });
      });
      it("sees new intervention materials when scoring over instructional target", () => {
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            cy.findByText("Select Activity")
              .scrollIntoView()
              .should("be.visible")
              .click();
            cy.findByText("Cover, Copy, Compare").isPseudoVisible();
            cy.findByText("Guided Practice").isPseudoVisible();
            cy.findByText("Bingo").isPseudoVisible();
          });

        const betweenInstructionalAndMasteryTargetsScore = "39";
        const testGoalScore = "20";
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            enterAndPreviewInterventionAndGoalSkillScores(betweenInstructionalAndMasteryTargetsScore, testGoalScore);
            cy.contains(`Intervention Skill score of ${betweenInstructionalAndMasteryTargetsScore}`).should(
              "be.visible"
            );
            cy.contains("! Excellent work. There will be new practice materials next time.").should("be.visible");
            cy.contains("Bruce Patrick also improved on the Goal Skill!").should("be.visible");
            cy.contains("Save the scores to continue the intervention").should("be.visible");

            clickVisibleTestId("saveScoreBtn");
          });
        cy.contains(/Sums to 6.*Fluency/).should("be.visible");
        expandInterventionForStudent("Bruce Patrick");
        cy.findByText("Bruce is doing well but still needs more practice with this intervention skill", {
          exact: false
        });
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            cy.findByText("is currently practicing the skill").should("be.visible");
            cy.findByText("Enter Scores to Continue").should("be.visible");

            cy.findByText("Select Activity")
              .scrollIntoView()
              .should("be.visible")
              .click();
            cy.findByText("Timed Trial").isPseudoVisible();
            cy.findByText("Response Cards").isPseudoVisible();
          });
      });
      it("practices intervention skill until only goal skill remains", () => {
        const passingInterventionScore = "99";
        const failingGoalScore = "0";
        verifyInterventionSkillHasDetailText("Sums to 6", brucePatrickStudentId);
        verifyNumberOfGoalAndInterventionSkillPoints(1, 2);

        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            enterAndSaveInterventionAndGoalSkillScores(passingInterventionScore, failingGoalScore);
          });
        cy.contains(/Sums to 12.*Acquisition/).should("be.visible");
        expandInterventionForStudent("Bruce Patrick");
        verifyNumberOfGoalAndInterventionSkillPoints(2, 0);

        // cy.findByTestId("manual-intervention-video-button").scrollIntoView();
        cy.findByText("Great work, Bruce is ready to start working on a new intervention skill!")
          .scrollIntoView()
          .should("be.visible");
        verifyInterventionSkillHasDetailText("Sums to 12", brucePatrickStudentId);
        verifyTwoSkillButtonLabelsInSkillContainer("Sums to 12", "Division 0-9");
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            enterAndSaveInterventionAndGoalSkillScores(passingInterventionScore, failingGoalScore);
          });
        cy.contains(/Multiplication 0-5.*Acquisition/).should("be.visible");
        expandInterventionForStudent("Bruce Patrick");
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            enterAndSaveInterventionAndGoalSkillScores(passingInterventionScore, failingGoalScore);
          });
        cy.contains(/Multiplication 0-9.*Acquisition/)
          .scrollIntoView()
          .should("be.visible");
        expandInterventionForStudent("Bruce Patrick");
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            enterAndSaveInterventionAndGoalSkillScores(passingInterventionScore, failingGoalScore);
          });
        cy.findByText("Division 0-5")
          .scrollIntoView()
          .should("be.visible");
        expandInterventionForStudent("Bruce Patrick");
        // cy.findByTestId("manual-intervention-video-button").scrollIntoView();
        cy.findByText("We need to administer a quick assessment to determine the best intervention for Bruce.")
          .scrollIntoView()
          .should("be.visible");
        verifyInterventionSkillHasDetailText("Division 0-5", brucePatrickStudentId);
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveInterventionScore(passingInterventionScore);
          });
        cy.contains(/Division 0-9.*Acquisition/)
          .scrollIntoView()
          .should("be.visible");
        expandInterventionForStudent("Bruce Patrick");
        verifyInterventionSkillHasDetailText("Division 0-9", brucePatrickStudentId);

        // cy.findByTestId("manual-intervention-video-button").scrollIntoView();
        cy.findByText("We have found an appropriate intervention for Bruce to practice.")
          .scrollIntoView()
          .should("be.visible");
        cy.findByText("Rate of Improvement", { exact: false })
          .scrollIntoView()
          .should("be.visible");
        cy.get(".roi").should("be.visible");

        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            cy.findByText("Select Activity")
              .scrollIntoView()
              .should("be.visible")
              .click();
            cy.findByText("Cover, Copy, Compare").isPseudoVisible();
            cy.findByText("Guided Practice").isPseudoVisible();
            cy.findByText("Select Activity").click();
          });

        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveGoalScore(passingInterventionScore);
          });
        continueInterventionToTheNextSkillTree(brucePatrickStudentId);
        cy.findAllByText("Fact Families: Multiplication/Division 0-9").should("be.visible");
        expandInterventionForStudent("Bruce Patrick");

        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveInterventionScore(0);
          });
        cy.findAllByText("Multiplication 0-9").should("be.visible");
        expandInterventionForStudent("Bruce Patrick");

        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveInterventionScore(passingInterventionScore);
          });
        cy.findByText("Division 0-9")
          .scrollIntoView()
          .should("be.visible");
        expandInterventionForStudent("Bruce Patrick");

        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveInterventionScore(passingInterventionScore);
          });
        cy.contains(/Fact Families: Multiplication\/Division 0-9.*Acquisition/)
          .scrollIntoView()
          .should("be.visible");
        expandInterventionForStudent("Bruce Patrick");

        // cy.findByTestId("manual-intervention-video-button").scrollIntoView();
        cy.findByText("We have found an appropriate intervention for Bruce to practice.")
          .scrollIntoView()
          .should("be.visible");

        verifyInterventionSkillHasDetailText("Fact Families: Multiplication/Division 0-9", brucePatrickStudentId);
      });
      it("practices remaining goal skills until the individual intervention is completed", () => {
        inputAndSaveGoalScore("99");
        cy.findByText("Congratulations, Bruce has completed all goal skills for Winter!").should("be.visible");
        cy.get(brucePatrickIIContainerSelector)
          .first()
          .within(() => {
            clickVisibleTestId("dismissInterventionMessage");
          });
        clickDashboardTab(TEST_GROUPS.grade3group2.name, "studentsTab");
        cy.findByText("Roster").should("be.visible");
        cy.get("[data-testid^='currentIntAssessmentRow_']").should("have.length", 1);
      });
    });
  });

  describe("Manage Individual Interventions:", () => {
    describe("Admin", () => {
      beforeEach(() => {
        cy.loginAs({ role: "coach", queryString: "?siteId=test_elementary_site_id" });
        clickElementByTextWithinSideNav("3rd Grade");
        cy.findByTestId("screeningNotice")
          .should("be.visible")
          .within(() => {
            cy.findByText(TEST_GROUPS.grade3group2.name, { exact: false }).click();
          });
        clickDashboardTab(TEST_GROUPS.grade3group2.name, "studentsTab");
      });
      it("forces individual intervention on a student not recommended for individual intervention", () => {
        manuallyScheduleInterventionForStudent(aliceAustinStudentId);
        cy.findByTestId(`individual_${aliceAustinStudentId}`).should("be.visible");
        clickDashboardTab(TEST_GROUPS.grade3group2.name, "individualInterventionTab");
        cy.findAllByText("Multiplication 0-9").should("be.visible");
        expandInterventionForStudent("Alice Austin");
        cy.get(aliceAustinIIContainerSelector)
          .first()
          .within(() => {
            inputAndSaveInterventionScore("50");
          });
        continueInterventionToTheNextSkillTree(aliceAustinStudentId);
        cy.findAllByText("Division 0-9").should("be.visible");
      });
      it("ends individual intervention for a student", () => {
        manuallyEndInterventionForStudent(aliceAustinStudentId);
        cy.findByText("Eligible for Individual Intervention").should("not.exist");
      });
      it("re-schedules interventions using available options", () => {
        clickDashboardTab(TEST_GROUPS.grade3group2.name, "studentsTab");
        openManageInterventionModal(aliceAustinStudentId);
        chooseManageInterventionOption("noAction");
        cy.findByText("Eligible for Individual Intervention").should("not.exist");
        openManageInterventionModal(aliceAustinStudentId);
        chooseManageInterventionOption("resumeIntervention");
        cy.findByText("Intervention resumed").should("be.visible");
        cy.findByText("Eligible for Individual Intervention").should("not.exist");
        cy.findAllByText("Austin, Alice")
          .first()
          .click();
        cy.findByText("Individual Intervention Progress").should("be.visible"); // the previous intervention was resumed and the progress was kept
        cy.findByText("Back to All Students").click();
        manuallyEndInterventionForStudent(aliceAustinStudentId);
        cy.findByText("Eligible for Individual Intervention").should("not.exist");
        openManageInterventionModal(aliceAustinStudentId);
        chooseManageInterventionOption("removeInterventions");
        cy.findByText("Intervention removed").should("be.visible");
        cy.findByText("Individual Intervention Students", { exact: false }).click();
        cy.findAllByText("Austin, Alice")
          .first()
          .click();
        cy.findByText("Individual Intervention Progress", { timeout: 5000 }).should("not.exist"); // the previous intervention was removed and there is no progress
      });
    });
  });
});
