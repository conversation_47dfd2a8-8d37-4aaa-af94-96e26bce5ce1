# This section will create a secret in the Kubernetes cluster.
# We need this for private docker repos.
apiVersion: v1
kind: Secret
metadata:
  name: stage-springmathcreds
data:
  .dockerconfigjson: DOCKER_CONFIG
type: kubernetes.io/dockerconfigjson
---
apiVersion: v1
kind: Secret
metadata:
  name: stage-springmathenvs
data:
  STAGE_ROOT_URL: STAGE_ROOT_URL_SECRET
  STAGE_MONGO_URL: STAGE_MONGO_URL_SECRET
  STAGE_MONGO_OPLOG_URL: STAGE_MONGO_OPLOG_URL_SECRET
  STAGE_PORT: STAGE_PORT_SECRET
  STAGE_METEOR_SETTINGS: STAGE_METEOR_SETTINGS_SECRET
---
# This section will create a deployment in the Kubernetes cluster
apiVersion: apps/v1
kind: Deployment
metadata:
  name: springmath-stage
  labels:
    app: springmath-stage
spec:
  selector:
    matchLabels:
      app: springmath-stage
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: springmath-stage
    spec:
      containers:
        - env:
            - name: ROOT_URL
              valueFrom:
                secretKeyRef:
                  key: STAGE_ROOT_URL
                  name: stage-springmathenvs
            - name: MONGO_URL
              valueFrom:
                secretKeyRef:
                  key: STAGE_MONGO_URL
                  name: stage-springmathenvs
            - name: MONGO_OPLOG_URL
              valueFrom:
                secretKeyRef:
                  key: STAGE_MONGO_OPLOG_URL
                  name: stage-springmathenvs
            - name: PORT
              valueFrom:
                secretKeyRef:
                  key: STAGE_PORT
                  name: stage-springmathenvs
            - name: METEOR_SETTINGS
              valueFrom:
                secretKeyRef:
                  key: STAGE_METEOR_SETTINGS
                  name: stage-springmathenvs
            - name: NODE_OPTIONS
              value: --max-old-space-size=1024
          image: IMAGE_FULL_TAG
          imagePullPolicy: Always
          name: springmath-stage
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              cpu: 1
              memory: 1Gi
      hostAliases:
        - ip: ***********
          hostnames:
            - springmath-mongo-stage
        - ip: "***********"
          hostnames:
            - "eschool.mpasd.net"
      imagePullSecrets:
        - name: stage-springmathcreds
---
# Ingress using Octavia controller for OVHcloud OpenStack
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: springmath-stage-ingress
  annotations:
    # --- external DNS / CF ---
    external-dns.alpha.kubernetes.io/hostname: app.stage.springmath.org
    external-dns.alpha.kubernetes.io/cloudflare-proxied: "true"

    # --- ingress class ---
    kubernetes.io/ingress.class: nginx

    # --- sticky‑session tuning ---
    nginx.ingress.kubernetes.io/affinity: "cookie" # must literally be “cookie”
    nginx.ingress.kubernetes.io/affinity-mode: "persistent" # rewrites bad cookies; replaces your “balanced”
    nginx.ingress.kubernetes.io/session-cookie-name: SPRINGMATH_STICKY_STAGE
    nginx.ingress.kubernetes.io/session-cookie-path: "/"

    nginx.ingress.kubernetes.io/session-cookie-expires: "28800" # 8 hrs
    nginx.ingress.kubernetes.io/session-cookie-max-age: "28800"

    # --- upstream timeouts ---
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
spec:
  tls:
    - hosts:
        - app.stage.springmath.org
      secretName: cloudflare-springmath-tls # Your existing Cloudflare SSL certificate
  rules:
    - host: app.stage.springmath.org
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: springmath-stage-service
                port:
                  number: 80
---
apiVersion: v1
kind: Service
metadata:
  name: springmath-stage-service
  labels:
    app: springmath-stage
spec:
  type: ClusterIP
  selector:
    app: springmath-stage
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 3000
