# This section will create a secret in the Kubernetes cluster.
# We need this for private docker repos.
apiVersion: v1
kind: Secret
metadata:
  name: prod-springmathcreds
data:
  .dockerconfigjson: DOCKER_CONFIG
type: kubernetes.io/dockerconfigjson
---
apiVersion: v1
kind: Secret
metadata:
  name: prod-springmathenvs
data:
  PROD_ROOT_URL: PROD_ROOT_URL_SECRET
  PROD_MONGO_URL: PROD_MONGO_URL_SECRET
  PROD_MONGO_OPLOG_URL: PROD_MONGO_OPLOG_URL_SECRET
  PROD_PORT: PROD_PORT_SECRET
  PROD_METEOR_SETTINGS: PROD_METEOR_SETTINGS_SECRET
---
# This section will create a deployment in the Kubernetes cluster
apiVersion: apps/v1
kind: Deployment
metadata:
  name: springmath-prod
  labels:
    app: springmath-prod
spec:
  selector:
    matchLabels:
      app: springmath-prod
  replicas: 6
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: springmath-prod
    spec:
      containers:
        - env:
            - name: ROOT_URL
              valueFrom:
                secretKeyRef:
                  key: PROD_ROOT_URL
                  name: prod-springmathenvs
            - name: MONGO_URL
              valueFrom:
                secretKeyRef:
                  key: PROD_MONGO_URL
                  name: prod-springmathenvs
            - name: MONGO_OPLOG_URL
              valueFrom:
                secretKeyRef:
                  key: PROD_MONGO_OPLOG_URL
                  name: prod-springmathenvs
            - name: PORT
              valueFrom:
                secretKeyRef:
                  key: PROD_PORT
                  name: prod-springmathenvs
            - name: METEOR_SETTINGS
              valueFrom:
                secretKeyRef:
                  key: PROD_METEOR_SETTINGS
                  name: prod-springmathenvs
            - name: NODE_OPTIONS
              value: --max-old-space-size=2048
          image: IMAGE_FULL_TAG
          imagePullPolicy: Always
          name: springmath-prod
          resources:
            limits:
              cpu: 1
              memory: 2Gi
            requests:
              cpu: 1
              memory: 2Gi
      hostAliases:
        - ip: "***********"
          hostnames:
            - "eschool.mpasd.net"
      # hostAliases:
      #   - ip: **********
      #     hostnames:
      #       - sm-mongo-test
      imagePullSecrets:
        - name: prod-springmathcreds
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: springmath-prod-ingress
  annotations:
    # --- external DNS / CF ---
    external-dns.alpha.kubernetes.io/hostname: app.springmath.org
    external-dns.alpha.kubernetes.io/cloudflare-proxied: "true"

    # --- ingress class ---
    kubernetes.io/ingress.class: nginx

    # --- sticky‑session tuning ---
    nginx.ingress.kubernetes.io/affinity: "cookie" # must literally be “cookie”
    nginx.ingress.kubernetes.io/affinity-mode: "persistent" # rewrites bad cookies; replaces your “balanced”
    nginx.ingress.kubernetes.io/session-cookie-name: SPRINGMATH_STICKY_PROD
    nginx.ingress.kubernetes.io/session-cookie-path: "/"

    nginx.ingress.kubernetes.io/session-cookie-expires: "28800" # 8 hrs
    nginx.ingress.kubernetes.io/session-cookie-max-age: "28800"

    # --- upstream timeouts ---
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
spec:
  tls:
    - hosts:
        - app.springmath.org
      secretName: cloudflare-springmath-tls # Your existing Cloudflare SSL certificate
  rules:
    - host: app.springmath.org
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: springmath-prod-service
                port:
                  number: 80
---
apiVersion: v1
kind: Service
metadata:
  name: springmath-prod-service
  labels:
    app: springmath-prod
spec:
  type: ClusterIP
  selector:
    app: springmath-prod
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 3000
