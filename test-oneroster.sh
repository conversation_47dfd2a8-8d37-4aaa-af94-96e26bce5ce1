#!/bin/bash

# OneRoster API test
API_URL="https://eschool.mpasd.net/Plus360Api/ims/oneroster"
OAUTH_URL="https://eschool.mpasd.net/K12AuthProvider/connect/token"
CLIENT_ID="OneRoster_MPA_553-35a40fea09a84163b7c47bf9bab73318"
CLIENT_SECRET="$1"  # Pass as first argument for security

if [ -z "$CLIENT_SECRET" ]; then
    echo "Usage: $0 <client_secret>"
    exit 1
fi

echo "Getting OAuth token..."
TOKEN_RESPONSE=$(curl -s -X POST "$OAUTH_URL" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "grant_type=client_credentials" \
    -d "client_id=$CLIENT_ID" \
    -d "client_secret=$CLIENT_SECRET" \
    -d "scope=oneroster" \
    -w "\nHTTP_STATUS:%{http_code}")

HTTP_STATUS=$(echo "$TOKEN_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
TOKEN_BODY=$(echo "$TOKEN_RESPONSE" | grep -v "HTTP_STATUS:")

echo "OAuth Response Status: $HTTP_STATUS"
if [ "$HTTP_STATUS" != "200" ]; then
    echo "OAuth Response Body: $TOKEN_BODY"
    exit 1
fi

ACCESS_TOKEN=$(echo "$TOKEN_BODY" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
    echo "Failed to get access token"
    echo "Response: $TOKEN_BODY"
    exit 1
fi

echo "Testing OneRoster API endpoint..."
curl -v -H "Authorization: Bearer $ACCESS_TOKEN" \
     "$API_URL/v1p1/orgs" \
     -w "\nHTTP Status: %{http_code}\n"
