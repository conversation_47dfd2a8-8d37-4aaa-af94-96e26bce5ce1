name: Build, Test, and Deploy SpringMath Dev to OVHCloud

on:
  push:
    branches:
      - dev
  pull_request:
    branches:
      - dev

env:
  NODE_VERSION: '22.16.0'
  METEOR_RELEASE: '3.3'

jobs:
  # Unit tests run first - fast feedback
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache npm dependencies
        uses: actions/cache@v4
        with:
          path: app/node_modules
          key: npm-deps-v1-${{ runner.os }}-${{ hashFiles('app/package-lock.json') }}

      - name: Cache schoolScrubber dependencies
        uses: actions/cache@v4
        with:
          path: app/.scripts/schoolScrubber/node_modules
          key: school-scrubber-v1-${{ runner.os }}-${{ hashFiles('app/.scripts/schoolScrubber/package-lock.json') }}

      - name: Install Node dependencies
        working-directory: app
        run: |
          if [ ! -d node_modules ]; then
            npm ci
          fi
          if [ ! -d .scripts/schoolScrubber/node_modules ]; then
            cd .scripts/schoolScrubber && npm ci
          fi

      - name: Run unit tests
        working-directory: app
        run: |
          BABEL_ENV=unittesting npx jest --maxWorkers=2 --ci \
            --reporters=default --reporters=jest-junit
        env:
          JEST_JUNIT_OUTPUT_DIR: ./test-results
          JEST_JUNIT_OUTPUT_NAME: junit.xml

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: unit-test-results
          path: app/test-results/

  # Parallel E2E tests
  e2e-tests:
    # Skip E2E tests if DEV_SKIP_E2E_TESTS is set to 'true'
    if: vars.DEV_SKIP_E2E_TESTS != 'true'
    # Using ubuntu-latest (2 vCPUs, 7 GB RAM)
    # Alternative: ubuntu-latest-4-cores (4 vCPUs, 16 GB RAM) for better performance
    runs-on: ubuntu-latest
    needs: unit-tests
    strategy:
      fail-fast: false
      matrix:
        shard: [1, 2, 3, 4]

    # MongoDB service container with test data - runs in background throughout the job
    services:
      mongodb:
        image: ${{ vars.OVHCLOUD_REGISTRY_URL }}/${{ vars.OVHCLOUD_REGISTRY_PROJECT_NAME }}/spring-math-test-ci:8.1
        credentials:
          username: ${{ vars.OVHCLOUD_REGISTRY_USERNAME }}
          password: ${{ secrets.OVHCLOUD_REGISTRY_ACCESS_TOKEN }}
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.runCommand({ping: 1})'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache npm dependencies
        uses: actions/cache@v4
        with:
          path: app/node_modules
          key: npm-deps-v1-${{ runner.os }}-${{ hashFiles('app/package-lock.json') }}

      - name: Cache Cypress binary
        uses: actions/cache@v4
        with:
          path: ~/.cache/Cypress
          key: cypress-v1-${{ runner.os }}-${{ hashFiles('app/package-lock.json') }}

      - name: Cache Global Meteor
        uses: actions/cache@v4
        with:
          path: ~/.meteor
          key: meteor-global-v1-${{ runner.os }}-${{ hashFiles('app/.meteor/release') }}

      - name: Cache Local Meteor
        uses: actions/cache@v4
        with:
          path: app/.meteor/local
          key: meteor-local-v1-${{ runner.os }}-${{ hashFiles('app/.meteor/versions', 'app/.meteor/packages') }}

      - name: Install dependencies
        working-directory: app
        run: |
          if [ ! -d node_modules ]; then
            npm ci
          fi
          if [ ! -e $HOME/.meteor/meteor ]; then
            curl -sS https://install.meteor.com/?release=${{ env.METEOR_RELEASE }} | /bin/sh
          fi
          sudo ln -sf ~/.meteor/meteor /usr/local/bin/meteor

      - name: Install Cypress binary
        working-directory: app
        run: |
          # Check if Cypress binary exists and install if missing
          if ! npx cypress verify > /dev/null 2>&1; then
            echo "Cypress binary not found or corrupted, installing..."
            npx cypress install
          else
            echo "Cypress binary is already installed and verified"
          fi

      - name: Prepare test environment
        working-directory: app
        run: |
          # Copy test settings
          cp .scripts/settings-for-testing.json settings.json

      - name: Get test specs for this shard
        id: specs
        working-directory: app
        run: |
          # Get all spec files
          ALL_SPECS=$(find tests/cypress/integration -name "*.spec.js" 2>/dev/null | sort || true)

          if [ -z "$ALL_SPECS" ]; then
            echo "No test specs found, skipping E2E tests"
            echo "specs=" >> $GITHUB_OUTPUT
            exit 0
          fi

          TOTAL=$(echo "$ALL_SPECS" | wc -l)

          # Calculate shard boundaries
          SHARD_SIZE=$(( ($TOTAL + 3) / 4 ))
          START=$(( (${{ matrix.shard }} - 1) * $SHARD_SIZE + 1 ))
          END=$(( ${{ matrix.shard }} * $SHARD_SIZE ))

          # Get specs for this shard
          SPECS=$(echo "$ALL_SPECS" | sed -n "${START},${END}p" | tr '\n' ',' || true)
          echo "specs=${SPECS%,}" >> $GITHUB_OUTPUT

          # Display for logs
          echo "Shard ${{ matrix.shard }} running specs:"
          echo "$ALL_SPECS" | sed -n "${START},${END}p" | xargs -n1 basename || true

      - name: Debug system resources
        if: steps.specs.outputs.specs != ''
        run: |
          echo "=== System Resources ==="
          echo "CPU cores: $(nproc)"
          echo "Memory:"
          free -h
          echo ""
          echo "Disk space:"
          df -h /
          echo ""
          echo "=== Process list (top 10 by memory) ==="
          ps aux --sort=-%mem | head -11

      - name: Wait for MongoDB service to be ready
        if: steps.specs.outputs.specs != ''
        run: |
          echo "=== Waiting for MongoDB service to be ready ==="
          # Wait for MongoDB to be accessible
          for i in {1..10}; do
            if mongosh --host localhost:27017 --eval "db.runCommand({ping: 1})" > /dev/null 2>&1; then
              echo "✓ MongoDB service is ready!"
              break
            fi
            echo "Waiting for MongoDB... (attempt $i/10)"
            sleep 3
          done

      - name: Start Meteor application with external MongoDB
        if: steps.specs.outputs.specs != ''
        working-directory: app
        run: |
          echo "=== Starting Meteor at $(date '+%Y-%m-%d %H:%M:%S') ==="
          echo "Using external MongoDB service on port 27017"

          # Create a log file for Meteor output
          touch meteor-startup.log

          # Set up environment variables to connect to external MongoDB service
          # This matches the configuration used in CircleCI (.scripts/.variables/docker_db_variables.sh)
          export MONGO_URL="mongodb://127.0.0.1:27017/meteor?replicaSet=edspring"
          export MONGO_OPLOG_URL="mongodb://127.0.0.1:27017/local?replicaSet=edspring"
          export MONGO_URL_LOG="mongodb://127.0.0.1:27017/meteor?replicaSet=edspring"
          export MONGO_DDP_QUEUE_URL="mongodb://127.0.0.1:27017/meteor?replicaSet=edspring"
          export EDSPRING_REPORTING_MONGO_URL="mongodb://127.0.0.1:27017/meteor?replicaSet=edspring"
          export MONGO_IMPORT_URL="mongodb://127.0.0.1:27017/meteor?replicaSet=edspring"

          export ROOT_URL="http://localhost:3000"
          export METEOR_ENVIRONMENT=TEST
          export SERVER_WEBSOCKET_COMPRESSION=0
          export CI=true

          echo "MongoDB connection URL: $MONGO_URL"

          echo "Meteor Version $(meteor --version)"

          # Start Meteor with external MongoDB connection
          # Add METEOR_PROFILE=100 to debug meteor startup
          METEOR_DISABLE_OPTIMISTIC_CACHING=1 meteor --once --settings settings.json 2>&1 | tee -a meteor-startup.log &

          METEOR_PID=$!
          echo "Meteor PID: $METEOR_PID"

          echo "Waiting for Meteor application to be ready..."
          START_TIME=$(date +%s)

          until grep -q "App running at: http://localhost:3000" meteor-startup.log 2>/dev/null; do
            sleep 1
            CURRENT_TIME=$(date +%s)
            ELAPSED=$((CURRENT_TIME - START_TIME))

            # Add timeout protection, without cache it takes longer
            if [ $ELAPSED -ge 300 ]; then
              echo "✗ Timeout: Meteor failed to start within 300 seconds"
              echo "=== Last 20 lines of Meteor log ==="
              tail -20 meteor-startup.log
              exit 1
            fi
          done

          END_TIME=$(date +%s)
          STARTUP_TIME=$((END_TIME - START_TIME))
          echo "✓ Meteor application is ready after ${STARTUP_TIME} seconds!"

      - name: Run Cypress tests
        if: steps.specs.outputs.specs != ''
        working-directory: app
        run: |
          echo "=== Starting Cypress tests at $(date '+%Y-%m-%d %H:%M:%S') ==="
          echo "Memory before tests:"
          free -h

          npx cypress run \
            --headless \
            --config-file cypress.gh.config.js \
            --spec "${{ steps.specs.outputs.specs }}" \
            --reporter junit \
            --reporter-options "mochaFile=test-results/cypress-[hash].xml"

          echo "=== Cypress tests completed ==="
          echo "Memory after tests:"
          free -h

          # Check for Meteor crashes during tests
          if [ -f meteor-startup.log ]; then
            if grep -q "Error\|FATAL\|crashed" meteor-startup.log; then
              echo "=== Meteor errors detected during tests ==="
              grep -E "Error|FATAL|crashed" meteor-startup.log | tail -20
            fi
          fi
        env:
          CYPRESS_baseUrl: http://localhost:3000
          # Increase Cypress timeouts for slower environment
          CYPRESS_defaultCommandTimeout: 10000
          CYPRESS_requestTimeout: 10000
          CYPRESS_responseTimeout: 10000

      - name: Upload test artifacts
        if: always() && steps.specs.outputs.specs != ''
        uses: actions/upload-artifact@v4
        with:
          name: cypress-results-shard-${{ matrix.shard }}
          path: |
            app/cypress/screenshots/
            app/cypress/videos/
            app/test-results/
            app/meteor-startup.log

  # Build and deploy only after all tests pass (on push to dev)
  build-and-deploy:
    # Continue even if e2e-tests are skipped
    if: |
      always() && 
      github.event_name == 'push' && 
      github.ref == 'refs/heads/dev' &&
      needs.unit-tests.result == 'success' &&
      (needs.e2e-tests.result == 'success' || needs.e2e-tests.result == 'skipped')
    needs: [unit-tests, e2e-tests]
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to OVH Harbor Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.OVHCLOUD_REGISTRY_URL }}
          username: ${{ vars.OVHCLOUD_REGISTRY_USERNAME }}
          password: ${{ secrets.OVHCLOUD_REGISTRY_ACCESS_TOKEN }}

      - name: Kubernetes set context
        uses: Azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.OVHCLOUD_TEST_KUBE_CONFIG }}

      - name: Docker Build and Push, Kubernetes apply
        run: |
          # Get the current version from the version.js file
          CURRENT_VERSION=$(grep -o '"[0-9]\+\.[0-9]\+\.[0-9]\+"' app/imports/startup/client/version.js | tr -d '"')
          echo "Current version: $CURRENT_VERSION"

          # Use git commit short hash as build number for uniqueness
          BUILD_NUMBER=$(git rev-parse --short HEAD)
          echo "Build number: $BUILD_NUMBER"

          # Create release tag with build number for dev environment
          export RELEASE="${CURRENT_VERSION}-${BUILD_NUMBER}-dev"
          echo "Release tag: $RELEASE"

          # split the pieces
          REGISTRY_REPO="${{ vars.OVHCLOUD_REGISTRY_URL }}/${{ vars.OVHCLOUD_REGISTRY_PROJECT_NAME }}/springmath-dev"

          docker build -t "$REGISTRY_REPO:$RELEASE" .
          docker push  "$REGISTRY_REPO:$RELEASE"

          # correct way to add 'latest'
          docker tag  "$REGISTRY_REPO:$RELEASE" "$REGISTRY_REPO:latest"
          docker push "$REGISTRY_REPO:latest"

          # set up sm secrets - encode plain text vars to base64
          echo 'Setting up Kubernetes secrets...'
          DEV_ROOT_URL_B64=$(echo -n "${{ vars.DEV_ROOT_URL }}" | base64 -w 0)
          DEV_PORT_B64=$(echo -n "${{ vars.METEOR_PORT }}" | base64 -w 0)

          sed -i'' -e "s|DEV_ROOT_URL_SECRET|${DEV_ROOT_URL_B64}|g" \
            -e "s|DEV_MONGO_URL_SECRET|${{ secrets.DEV_MONGO_URL }}|g" \
            -e "s|DEV_MONGO_OPLOG_URL_SECRET|${{ secrets.DEV_MONGO_OPLOG_URL }}|g" \
            -e "s|DEV_PORT_SECRET|${DEV_PORT_B64}|g" \
            -e "s|DEV_METEOR_SETTINGS_SECRET|${{ secrets.DEV_METEOR_SETTINGS }}|g" \
            ./k8/dev.yml

          # set up docker creds
          export DOCKER_CONFIG=$(cat ~/.docker/config.json | base64 -w 0)
          sed -i'' -e "s|IMAGE_FULL_TAG|$REGISTRY_REPO:$RELEASE|g" \
            -e "s|DOCKER_CONFIG|$DOCKER_CONFIG|g" \
            ./k8/dev.yml

          echo 'applying yml file'

          # apply test
          kubectl apply -f ./k8/dev.yml

          # Wait for deployment to complete
          kubectl rollout status deployment/springmath-dev --timeout=600s

          # Deploy appropriate ingress based on SSL provider
          export DEV_SSL_PROVIDER="${{ vars.DEV_SSL_PROVIDER || 'cloudflare' }}"
          echo "Deploying ingress with SSL provider: $DEV_SSL_PROVIDER"
          ./k8/nginx-ingress-controller/deploy-ingress.sh dev $DEV_SSL_PROVIDER

      - name: Purge Cloudflare cache
        uses: jakejarvis/cloudflare-purge-action@master
        env:
          CLOUDFLARE_ZONE: ${{ vars.SPRINGMATH_CLOUDFLARE_ZONE_ID }}
          CLOUDFLARE_TOKEN: ${{ secrets.SPRINGMATH_CLOUDFLARE_PURGE_TOKEN }}
          PURGE_URLS: '["https://app.dev.springmath.com/*"]'
